-- 创建数据库
CREATE DATABASE IF NOT EXISTS badminton_reservation 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE badminton_reservation;

-- 用户表
CREATE TABLE users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    password VARCHAR(255) NOT NULL COMMENT '密码',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '手机号',
    real_name VARCHAR(50) COMMENT '真实姓名',
    avatar VARCHAR(255) COMMENT '头像URL',
    role ENUM('USER', 'COACH', 'ADMIN', 'SUPER_ADMIN') DEFAULT 'USER' COMMENT '角色',
    status ENUM('ACTIVE', 'INACTIVE', 'BANNED') DEFAULT 'ACTIVE' COMMENT '状态',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '用户表';

-- 场地表
CREATE TABLE venues (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '场地名称',
    description TEXT COMMENT '场地描述',
    capacity INT DEFAULT 4 COMMENT '容纳人数',
    price_per_hour DECIMAL(10,2) NOT NULL COMMENT '每小时价格',
    equipment TEXT COMMENT '设备配置',
    images JSON COMMENT '场地图片',
    status ENUM('AVAILABLE', 'MAINTENANCE', 'DISABLED') DEFAULT 'AVAILABLE' COMMENT '状态',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '场地表';

-- 预约表
CREATE TABLE reservations (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL COMMENT '用户ID',
    venue_id BIGINT NOT NULL COMMENT '场地ID',
    start_time DATETIME NOT NULL COMMENT '开始时间',
    end_time DATETIME NOT NULL COMMENT '结束时间',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    status ENUM('PENDING_PAYMENT', 'CONFIRMED', 'COMPLETED', 'CANCELLED') DEFAULT 'PENDING_PAYMENT' COMMENT '状态',
    reservation_type ENUM('REGULAR', 'COACHING') DEFAULT 'REGULAR' COMMENT '预约类型',
    notes TEXT COMMENT '备注',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (venue_id) REFERENCES venues(id),
    INDEX idx_venue_time (venue_id, start_time, end_time),
    INDEX idx_user_time (user_id, start_time)
) COMMENT '预约表';

-- 订单表
CREATE TABLE orders (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    reservation_id BIGINT NOT NULL COMMENT '预约ID',
    order_no VARCHAR(32) UNIQUE NOT NULL COMMENT '订单号',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    payment_method ENUM('ALIPAY', 'WECHAT', 'CASH') COMMENT '支付方式',
    payment_status ENUM('PENDING', 'PAID', 'REFUNDED', 'FAILED') DEFAULT 'PENDING' COMMENT '支付状态',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    deleted TINYINT DEFAULT 0 COMMENT '逻辑删除',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    FOREIGN KEY (reservation_id) REFERENCES reservations(id)
) COMMENT '订单表';

-- 系统配置表
CREATE TABLE system_config (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) COMMENT '系统配置表';

-- 插入默认管理员用户
INSERT INTO users (username, password, email, real_name, role) VALUES 
('admin', '$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM8lE9lBOsl7iKTVEFDi', '<EMAIL>', '系统管理员', 'SUPER_ADMIN');

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES 
('business_hours_start', '08:00', '营业开始时间'),
('business_hours_end', '22:00', '营业结束时间'),
('advance_booking_days', '7', '提前预约天数'),
('cancellation_hours', '2', '取消预约提前小时数');