# 羽毛球馆预约管理系统 (C语言版)

## 项目简介

这是一个使用C语言开发的羽毛球馆预约管理系统，提供完整的场地预约、用户管理、订单处理等功能。

## 功能特性

### 用户功能
- 用户注册和登录
- 查看可用场地信息
- 预约场地
- 查看个人预约记录
- 查看个人订单
- 在线支付订单
- 取消预约
- 修改个人信息
- 修改密码

### 管理员功能
- 场地管理（增删改查）
- 预约管理（查看所有预约）
- 订单管理（查看所有订单）
- 用户管理（开发中）
- 数据统计（开发中）

### 系统特性
- 数据持久化存储
- 时间冲突检测
- 输入验证
- 角色权限控制
- 简洁的控制台界面

## 技术架构

- **编程语言**: C语言 (C99标准)
- **数据存储**: 二进制文件存储
- **编译工具**: GCC
- **构建工具**: Make

## 项目结构

```
badminton-reservation-c/
├── include/
│   └── badminton.h          # 头文件，包含所有结构体和函数声明
├── src/
│   ├── main.c               # 主程序入口
│   ├── system.c             # 系统初始化
│   ├── data.c               # 数据持久化
│   ├── user.c               # 用户管理
│   ├── venue.c              # 场地管理
│   ├── reservation.c        # 预约管理
│   ├── order.c              # 订单管理
│   ├── utils.c              # 工具函数
│   └── menu.c               # 菜单系统
├── data/                    # 数据文件目录（运行时创建）
├── build/                   # 编译输出目录
├── Makefile                 # 编译配置
└── README.md               # 项目说明
```

## 编译和运行

### 环境要求
- GCC编译器
- Make工具
- 支持C99标准的系统

### 编译步骤

1. **克隆或下载项目**
   ```bash
   cd badminton-reservation-c
   ```

2. **编译项目**
   ```bash
   make
   ```

3. **运行程序**
   ```bash
   make run
   # 或直接运行
   ./badminton_system
   ```

### 其他编译选项

- **调试版本**
  ```bash
  make debug
  ```

- **发布版本**
  ```bash
  make release
  ```

- **清理编译文件**
  ```bash
  make clean
  ```

- **清理所有文件（包括数据）**
  ```bash
  make clean-all
  ```

## 使用说明

### 首次运行
1. 系统会自动创建默认管理员账户：
   - 用户名: `admin`
   - 密码: `admin123`

2. 系统会创建两个默认场地供测试使用

### 用户操作流程
1. **注册新用户**: 在主菜单选择"用户注册"
2. **登录系统**: 使用用户名和密码登录
3. **查看场地**: 浏览可用的羽毛球场地
4. **预约场地**: 选择场地和时间进行预约
5. **支付订单**: 完成预约后进行支付确认
6. **管理预约**: 查看或取消已有预约

### 管理员操作流程
1. **登录管理员账户**: 使用admin账户登录
2. **场地管理**: 添加、修改、删除场地
3. **预约管理**: 查看所有用户的预约情况
4. **订单管理**: 监控所有订单状态

## 数据结构

### 主要实体
- **User**: 用户信息（ID、用户名、密码、邮箱、手机、角色等）
- **Venue**: 场地信息（ID、名称、描述、容量、价格、设备等）
- **Reservation**: 预约信息（ID、用户ID、场地ID、时间、金额、状态等）
- **Order**: 订单信息（ID、预约ID、订单号、金额、支付状态等）

### 枚举类型
- **UserRole**: 用户角色（普通用户、教练、管理员、超级管理员）
- **UserStatus**: 用户状态（活跃、非活跃、已禁用）
- **VenueStatus**: 场地状态（可用、维护中、已禁用）
- **ReservationStatus**: 预约状态（待支付、已确认、已完成、已取消）
- **PaymentStatus**: 支付状态（待支付、已支付、已退款、支付失败）

## 系统限制

- 最大用户数: 1000
- 最大场地数: 50
- 最大预约数: 2000
- 最大订单数: 2000
- 用户名长度: 50字符
- 密码长度: 50字符
- 邮箱长度: 100字符
- 手机号长度: 20字符
- 描述长度: 200字符

## 开发计划

### 已完成功能
- ✅ 基础系统架构
- ✅ 用户注册和登录
- ✅ 场地管理
- ✅ 预约管理
- ✅ 订单管理
- ✅ 数据持久化
- ✅ 基础菜单系统

### 待开发功能
- 🔄 用户管理（管理员功能）
- 🔄 数据统计和报表
- 🔄 场地修改功能
- 🔄 高级搜索和筛选
- 🔄 邮件通知功能
- 🔄 数据备份和恢复

## 注意事项

1. **数据安全**: 密码以明文存储，生产环境需要加密
2. **并发控制**: 当前版本不支持多用户并发访问
3. **输入验证**: 已实现基础验证，可进一步加强
4. **错误处理**: 已实现基础错误处理机制
5. **内存管理**: 使用静态数组，注意容量限制

## 许可证

本项目仅供学习和研究使用。

## 联系方式

如有问题或建议，请联系开发团队。