#include "../include/badminton.h"

int add_venue(const char* name, const char* description, int capacity, double price_per_hour, const char* equipment) {
    if (system_data.venue_count >= MAX_VENUES) {
        printf("错误：场地数量已达上限！\n");
        return 0;
    }
    
    Venue* new_venue = &system_data.venues[system_data.venue_count];
    new_venue->id = system_data.next_venue_id++;
    strcpy(new_venue->name, name);
    strcpy(new_venue->description, description);
    new_venue->capacity = capacity;
    new_venue->price_per_hour = price_per_hour;
    strcpy(new_venue->equipment, equipment);
    new_venue->status = VENUE_AVAILABLE;
    new_venue->created_at = time(NULL);
    new_venue->updated_at = time(NULL);
    
    system_data.venue_count++;
    
    printf("场地添加成功！场地ID: %d\n", new_venue->id);
    return new_venue->id;
}

int update_venue(int venue_id, const char* name, const char* description, int capacity, double price_per_hour, const char* equipment) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            strcpy(system_data.venues[i].name, name);
            strcpy(system_data.venues[i].description, description);
            system_data.venues[i].capacity = capacity;
            system_data.venues[i].price_per_hour = price_per_hour;
            strcpy(system_data.venues[i].equipment, equipment);
            system_data.venues[i].updated_at = time(NULL);
            
            printf("场地信息更新成功！\n");
            return 1;
        }
    }
    printf("错误：场地不存在！\n");
    return 0;
}int delete_venue(int venue_id) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            // 检查是否有未完成的预约
            for (int j = 0; j < system_data.reservation_count; j++) {
                if (system_data.reservations[j].venue_id == venue_id && 
                    (system_data.reservations[j].status == RESERVATION_PENDING || 
                     system_data.reservations[j].status == RESERVATION_CONFIRMED)) {
                    printf("错误：该场地还有未完成的预约，无法删除！\n");
                    return 0;
                }
            }
            
            // 移动数组元素
            for (int k = i; k < system_data.venue_count - 1; k++) {
                system_data.venues[k] = system_data.venues[k + 1];
            }
            system_data.venue_count--;
            
            printf("场地删除成功！\n");
            return 1;
        }
    }
    printf("错误：场地不存在！\n");
    return 0;
}

Venue* get_venue_by_id(int venue_id) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            return &system_data.venues[i];
        }
    }
    return NULL;
}

void list_venues(void) {
    printf("\n==================== 场地列表 ====================\n");
    printf("ID\t名称\t\t容量\t价格/小时\t状态\n");
    printf("================================================\n");
    
    for (int i = 0; i < system_data.venue_count; i++) {
        const char* status_str;
        switch (system_data.venues[i].status) {
            case VENUE_AVAILABLE: status_str = "可用"; break;
            case VENUE_MAINTENANCE: status_str = "维护中"; break;
            case VENUE_DISABLED: status_str = "已禁用"; break;
            default: status_str = "未知"; break;
        }
        
        printf("%d\t%-12s\t%d\t%.2f\t\t%s\n", 
               system_data.venues[i].id,
               system_data.venues[i].name,
               system_data.venues[i].capacity,
               system_data.venues[i].price_per_hour,
               status_str);
    }
    printf("================================================\n");
}

void list_available_venues(void) {
    printf("\n================= 可用场地列表 =================\n");
    printf("ID\t名称\t\t容量\t价格/小时\t描述\n");
    printf("================================================\n");
    
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].status == VENUE_AVAILABLE) {
            printf("%d\t%-12s\t%d\t%.2f\t\t%s\n", 
                   system_data.venues[i].id,
                   system_data.venues[i].name,
                   system_data.venues[i].capacity,
                   system_data.venues[i].price_per_hour,
                   system_data.venues[i].description);
        }
    }
    printf("================================================\n");
}
int set_venue_status(int venue_id, VenueStatus status) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            // 检查状态转换的合法性
            if (status < VENUE_AVAILABLE || status > VENUE_DISABLED) {
                printf("错误：无效的场地状态！\n");
                return 0;
            }
            
            // 如果要禁用场地，检查是否有未完成的预约
            if (status == VENUE_DISABLED || status == VENUE_MAINTENANCE) {
                for (int j = 0; j < system_data.reservation_count; j++) {
                    if (system_data.reservations[j].venue_id == venue_id && 
                        (system_data.reservations[j].status == RESERVATION_PENDING || 
                         system_data.reservations[j].status == RESERVATION_CONFIRMED) &&
                        system_data.reservations[j].start_time > time(NULL)) {
                        printf("警告：该场地有未来的预约，建议先处理预约后再修改状态！\n");
                        printf("是否继续修改状态？(y/n): ");
                        char confirm;
                        scanf(" %c", &confirm);
                        if (confirm != 'y' && confirm != 'Y') {
                            printf("操作已取消。\n");
                            return 0;
                        }
                    }
                }
            }
            
            VenueStatus old_status = system_data.venues[i].status;
            system_data.venues[i].status = status;
            system_data.venues[i].updated_at = time(NULL);
            
            const char* old_status_str = (old_status == VENUE_AVAILABLE) ? "可用" : 
                                        (old_status == VENUE_MAINTENANCE) ? "维护中" : "已禁用";
            const char* new_status_str = (status == VENUE_AVAILABLE) ? "可用" : 
                                        (status == VENUE_MAINTENANCE) ? "维护中" : "已禁用";
            
            printf("场地状态更新成功！%s -> %s\n", old_status_str, new_status_str);
            return 1;
        }
    }
    printf("错误：场地不存在！\n");
    return 0;
}

int is_venue_available(int venue_id) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            return system_data.venues[i].status == VENUE_AVAILABLE;
        }
    }
    return 0; // 场地不存在，视为不可用
}

void show_venue_details(int venue_id) {
    Venue* venue = get_venue_by_id(venue_id);
    if (venue == NULL) {
        printf("错误：场地不存在！\n");
        return;
    }
    
    printf("\n==================== 场地详情 ====================\n");
    printf("场地ID：%d\n", venue->id);
    printf("场地名称：%s\n", venue->name);
    printf("场地描述：%s\n", venue->description);
    printf("容纳人数：%d人\n", venue->capacity);
    printf("每小时价格：%.2f元\n", venue->price_per_hour);
    printf("设备配置：%s\n", venue->equipment);
    
    const char* status_str;
    switch (venue->status) {
        case VENUE_AVAILABLE: status_str = "可用"; break;
        case VENUE_MAINTENANCE: status_str = "维护中"; break;
        case VENUE_DISABLED: status_str = "已禁用"; break;
        default: status_str = "未知"; break;
    }
    printf("当前状态：%s\n", status_str);
    
    char created_time[64], updated_time[64];
    strftime(created_time, sizeof(created_time), "%Y-%m-%d %H:%M:%S", localtime(&venue->created_at));
    strftime(updated_time, sizeof(updated_time), "%Y-%m-%d %H:%M:%S", localtime(&venue->updated_at));
    
    printf("创建时间：%s\n", created_time);
    printf("更新时间：%s\n", updated_time);
    
    // 显示今日预约情况
    printf("\n今日预约情况：\n");
    time_t now = time(NULL);
    struct tm* today = localtime(&now);
    today->tm_hour = 0;
    today->tm_min = 0;
    today->tm_sec = 0;
    time_t today_start = mktime(today);
    time_t today_end = today_start + 24 * 60 * 60;
    
    int reservation_count = 0;
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].venue_id == venue_id &&
            system_data.reservations[i].start_time >= today_start &&
            system_data.reservations[i].start_time < today_end &&
            (system_data.reservations[i].status == RESERVATION_PENDING ||
             system_data.reservations[i].status == RESERVATION_CONFIRMED)) {
            
            if (reservation_count == 0) {
                printf("时间段\t\t\t状态\n");
                printf("----------------------------------------\n");
            }
            
            char start_time[32], end_time[32];
            strftime(start_time, sizeof(start_time), "%H:%M", localtime(&system_data.reservations[i].start_time));
            strftime(end_time, sizeof(end_time), "%H:%M", localtime(&system_data.reservations[i].end_time));
            
            const char* res_status = (system_data.reservations[i].status == RESERVATION_PENDING) ? "待确认" : "已确认";
            printf("%s - %s\t\t%s\n", start_time, end_time, res_status);
            reservation_count++;
        }
    }
    
    if (reservation_count == 0) {
        printf("今日暂无预约\n");
    }
    
    printf("================================================\n");
}