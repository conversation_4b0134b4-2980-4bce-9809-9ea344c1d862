#include "../include/badminton.h"

int create_order(int reservation_id) {
    // 查找预约
    Reservation* reservation = NULL;
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].id == reservation_id) {
            reservation = &system_data.reservations[i];
            break;
        }
    }
    
    if (reservation == NULL) {
        printf("错误：预约不存在！\n");
        return 0;
    }
    
    if (reservation->status != RESERVATION_PENDING) {
        printf("错误：预约状态不正确！\n");
        return 0;
    }
    
    if (system_data.order_count >= MAX_ORDERS) {
        printf("错误：订单数量已达上限！\n");
        return 0;
    }
    
    // 创建订单
    Order* new_order = &system_data.orders[system_data.order_count];
    new_order->id = system_data.next_order_id++;
    new_order->reservation_id = reservation_id;
    generate_order_no(new_order->order_no);
    new_order->amount = reservation->total_amount;
    new_order->payment_status = PAYMENT_PENDING;
    new_order->paid_at = 0;
    new_order->created_at = time(NULL);
    
    system_data.order_count++;
    
    printf("订单创建成功！\n");
    printf("订单号: %s\n", new_order->order_no);
    printf("金额: %.2f 元\n", new_order->amount);
    
    return new_order->id;
}int process_payment(int order_id) {
    for (int i = 0; i < system_data.order_count; i++) {
        if (system_data.orders[i].id == order_id) {
            if (system_data.orders[i].payment_status == PAYMENT_PAID) {
                printf("错误：订单已经支付！\n");
                return 0;
            }
            
            // 模拟支付处理
            printf("正在处理支付...\n");
            printf("支付成功！\n");
            
            system_data.orders[i].payment_status = PAYMENT_PAID;
            system_data.orders[i].paid_at = time(NULL);
            
            // 更新预约状态
            for (int j = 0; j < system_data.reservation_count; j++) {
                if (system_data.reservations[j].id == system_data.orders[i].reservation_id) {
                    system_data.reservations[j].status = RESERVATION_CONFIRMED;
                    system_data.reservations[j].updated_at = time(NULL);
                    break;
                }
            }
            
            return 1;
        }
    }
    printf("错误：订单不存在！\n");
    return 0;
}

void list_user_orders(int user_id) {
    printf("\n================= 我的订单列表 =================\n");
    printf("订单号\t\t\t金额\t\t支付状态\t创建时间\n");
    printf("================================================\n");
    
    int found = 0;
    for (int i = 0; i < system_data.order_count; i++) {
        // 通过预约ID查找用户ID
        for (int j = 0; j < system_data.reservation_count; j++) {
            if (system_data.reservations[j].id == system_data.orders[i].reservation_id &&
                system_data.reservations[j].user_id == user_id) {
                found = 1;
                
                const char* status_str;
                switch (system_data.orders[i].payment_status) {
                    case PAYMENT_PENDING: status_str = "待支付"; break;
                    case PAYMENT_PAID: status_str = "已支付"; break;
                    case PAYMENT_REFUNDED: status_str = "已退款"; break;
                    case PAYMENT_FAILED: status_str = "支付失败"; break;
                    default: status_str = "未知"; break;
                }
                
                printf("%-20s\t%.2f\t\t%-8s\t%s\n",
                       system_data.orders[i].order_no,
                       system_data.orders[i].amount,
                       status_str,
                       format_time(system_data.orders[i].created_at));
                break;
            }
        }
    }
    
    if (!found) {
        printf("暂无订单记录\n");
    }
    printf("================================================\n");
}

void list_all_orders(void) {
    printf("\n================= 所有订单列表 =================\n");
    printf("订单号\t\t\t用户\t\t金额\t\t支付状态\t创建时间\n");
    printf("========================================================\n");
    
    for (int i = 0; i < system_data.order_count; i++) {
        // 查找用户名
        char username[MAX_NAME_LEN] = "未知用户";
        for (int j = 0; j < system_data.reservation_count; j++) {
            if (system_data.reservations[j].id == system_data.orders[i].reservation_id) {
                for (int k = 0; k < system_data.user_count; k++) {
                    if (system_data.users[k].id == system_data.reservations[j].user_id) {
                        strcpy(username, system_data.users[k].real_name);
                        break;
                    }
                }
                break;
            }
        }
        
        const char* status_str;
        switch (system_data.orders[i].payment_status) {
            case PAYMENT_PENDING: status_str = "待支付"; break;
            case PAYMENT_PAID: status_str = "已支付"; break;
            case PAYMENT_REFUNDED: status_str = "已退款"; break;
            case PAYMENT_FAILED: status_str = "支付失败"; break;
            default: status_str = "未知"; break;
        }
        
        printf("%-20s\t%-12s\t%.2f\t\t%-8s\t%s\n",
               system_data.orders[i].order_no,
               username,
               system_data.orders[i].amount,
               status_str,
               format_time(system_data.orders[i].created_at));
    }
    printf("========================================================\n");
}