#include "../include/badminton.h"

int create_order(int reservation_id) {
    // 查找预约
    Reservation* reservation = NULL;
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].id == reservation_id) {
            reservation = &system_data.reservations[i];
            break;
        }
    }
    
    if (reservation == NULL) {
        printf("错误：预约不存在！\n");
        return 0;
    }
    
    if (reservation->status != RESERVATION_PENDING) {
        printf("错误：预约状态不正确！\n");
        return 0;
    }
    
    if (system_data.order_count >= MAX_ORDERS) {
        printf("错误：订单数量已达上限！\n");
        return 0;
    }
    
    // 创建订单
    Order* new_order = &system_data.orders[system_data.order_count];
    new_order->id = system_data.next_order_id++;
    new_order->reservation_id = reservation_id;
    generate_order_no(new_order->order_no);
    new_order->amount = reservation->total_amount;
    new_order->payment_status = PAYMENT_PENDING;
    new_order->payment_method = 0;  // 未选择支付方式
    strcpy(new_order->transaction_id, "");
    strcpy(new_order->payment_desc, "羽毛球场地预约费用");
    new_order->paid_at = 0;
    new_order->created_at = time(NULL);
    new_order->timeout_at = time(NULL) + 30 * 60;  // 30分钟超时
    new_order->refund_amount = 0;
    new_order->refunded_at = 0;
    
    system_data.order_count++;
    
    printf("订单创建成功！\n");
    printf("订单号: %s\n", new_order->order_no);
    printf("金额: %.2f 元\n", new_order->amount);
    
    return new_order->id;
}int process_payment(int order_id) {
    for (int i = 0; i < system_data.order_count; i++) {
        if (system_data.orders[i].id == order_id) {
            if (system_data.orders[i].payment_status == PAYMENT_PAID) {
                printf("错误：订单已经支付！\n");
                return 0;
            }
            
            // 模拟支付处理
            printf("正在处理支付...\n");
            printf("支付成功！\n");
            
            system_data.orders[i].payment_status = PAYMENT_PAID;
            system_data.orders[i].paid_at = time(NULL);
            
            // 更新预约状态
            for (int j = 0; j < system_data.reservation_count; j++) {
                if (system_data.reservations[j].id == system_data.orders[i].reservation_id) {
                    system_data.reservations[j].status = RESERVATION_CONFIRMED;
                    system_data.reservations[j].updated_at = time(NULL);
                    break;
                }
            }
            
            return 1;
        }
    }
    printf("错误：订单不存在！\n");
    return 0;
}

void list_user_orders(int user_id) {
    printf("\n================= 我的订单列表 =================\n");
    printf("订单号\t\t\t金额\t\t支付状态\t创建时间\n");
    printf("================================================\n");
    
    int found = 0;
    for (int i = 0; i < system_data.order_count; i++) {
        // 通过预约ID查找用户ID
        for (int j = 0; j < system_data.reservation_count; j++) {
            if (system_data.reservations[j].id == system_data.orders[i].reservation_id &&
                system_data.reservations[j].user_id == user_id) {
                found = 1;
                
                const char* status_str;
                switch (system_data.orders[i].payment_status) {
                    case PAYMENT_PENDING: status_str = "待支付"; break;
                    case PAYMENT_PAID: status_str = "已支付"; break;
                    case PAYMENT_REFUNDED: status_str = "已退款"; break;
                    case PAYMENT_FAILED: status_str = "支付失败"; break;
                    default: status_str = "未知"; break;
                }
                
                printf("%-20s\t%.2f\t\t%-8s\t%s\n",
                       system_data.orders[i].order_no,
                       system_data.orders[i].amount,
                       status_str,
                       format_time(system_data.orders[i].created_at));
                break;
            }
        }
    }
    
    if (!found) {
        printf("暂无订单记录\n");
    }
    printf("================================================\n");
}

void list_all_orders(void) {
    printf("\n================= 所有订单列表 =================\n");
    printf("订单号\t\t\t用户\t\t金额\t\t支付状态\t创建时间\n");
    printf("========================================================\n");
    
    for (int i = 0; i < system_data.order_count; i++) {
        // 查找用户名
        char username[MAX_NAME_LEN] = "未知用户";
        for (int j = 0; j < system_data.reservation_count; j++) {
            if (system_data.reservations[j].id == system_data.orders[i].reservation_id) {
                for (int k = 0; k < system_data.user_count; k++) {
                    if (system_data.users[k].id == system_data.reservations[j].user_id) {
                        strcpy(username, system_data.users[k].real_name);
                        break;
                    }
                }
                break;
            }
        }
        
        const char* status_str;
        switch (system_data.orders[i].payment_status) {
            case PAYMENT_PENDING: status_str = "待支付"; break;
            case PAYMENT_PAID: status_str = "已支付"; break;
            case PAYMENT_REFUNDED: status_str = "已退款"; break;
            case PAYMENT_FAILED: status_str = "支付失败"; break;
            default: status_str = "未知"; break;
        }
        
        printf("%-20s\t%-12s\t%.2f\t\t%-8s\t%s\n",
               system_data.orders[i].order_no,
               username,
               system_data.orders[i].amount,
               status_str,
               format_time(system_data.orders[i].created_at));
    }
    printf("========================================================\n");
}int process_payment_with_method(int order_id, PaymentMethod method, const char* transaction_id) {
    Order* order = get_order_by_id(order_id);
    if (order == NULL) {
        printf("错误：订单不存在！\n");
        return 0;
    }
    
    if (order->payment_status == PAYMENT_PAID) {
        printf("错误：订单已经支付！\n");
        return 0;
    }
    
    if (order->payment_status == PAYMENT_CANCELLED || order->payment_status == PAYMENT_TIMEOUT) {
        printf("错误：订单已取消或超时！\n");
        return 0;
    }
    
    // 检查订单是否超时
    time_t now = time(NULL);
    if (now > order->timeout_at) {
        order->payment_status = PAYMENT_TIMEOUT;
        printf("错误：订单已超时！\n");
        return 0;
    }
    
    // 模拟不同支付方式的处理
    printf("正在使用%s支付...\n", get_payment_method_string(method));
    
    // 模拟支付处理时间
    printf("支付处理中");
    for (int i = 0; i < 3; i++) {
        printf(".");
        fflush(stdout);
        // 在实际应用中这里会调用第三方支付API
    }
    printf("\n");
    
    // 模拟支付成功（实际应用中需要验证支付结果）
    order->payment_status = PAYMENT_PAID;
    order->payment_method = method;
    order->paid_at = time(NULL);
    
    if (transaction_id && strlen(transaction_id) > 0) {
        strncpy(order->transaction_id, transaction_id, sizeof(order->transaction_id) - 1);
        order->transaction_id[sizeof(order->transaction_id) - 1] = '\0';
    } else {
        // 生成模拟交易号
        snprintf(order->transaction_id, sizeof(order->transaction_id), 
                "TXN_%ld_%d", time(NULL), order->id);
    }
    
    // 更新预约状态为已确认
    Reservation* reservation = get_reservation_by_id(order->reservation_id);
    if (reservation) {
        reservation->status = RESERVATION_CONFIRMED;
        reservation->updated_at = time(NULL);
    }
    
    printf("支付成功！\n");
    printf("交易号: %s\n", order->transaction_id);
    printf("支付方式: %s\n", get_payment_method_string(method));
    printf("支付金额: %.2f 元\n", order->amount);
    
    return 1;
}

int cancel_order(int order_id) {
    Order* order = get_order_by_id(order_id);
    if (order == NULL) {
        printf("错误：订单不存在！\n");
        return 0;
    }
    
    if (order->payment_status == PAYMENT_PAID) {
        printf("错误：已支付的订单不能取消，请使用退款功能！\n");
        return 0;
    }
    
    if (order->payment_status == PAYMENT_CANCELLED) {
        printf("错误：订单已经取消！\n");
        return 0;
    }
    
    order->payment_status = PAYMENT_CANCELLED;
    
    // 将对应的预约状态重置为待支付
    Reservation* reservation = get_reservation_by_id(order->reservation_id);
    if (reservation && reservation->status == RESERVATION_PENDING) {
        // 预约保持待支付状态，用户可以重新创建订单
        printf("订单取消成功，预约保持待支付状态。\n");
    }
    
    return 1;
}

int refund_order(int order_id, double refund_amount) {
    Order* order = get_order_by_id(order_id);
    if (order == NULL) {
        printf("错误：订单不存在！\n");
        return 0;
    }
    
    if (order->payment_status != PAYMENT_PAID) {
        printf("错误：只有已支付的订单才能退款！\n");
        return 0;
    }
    
    if (refund_amount <= 0 || refund_amount > order->amount) {
        printf("错误：退款金额无效！\n");
        return 0;
    }
    
    // 检查是否已经退款
    if (order->payment_status == PAYMENT_REFUNDED) {
        printf("错误：订单已经退款！\n");
        return 0;
    }
    
    printf("正在处理退款...\n");
    printf("退款方式: %s\n", get_payment_method_string(order->payment_method));
    printf("退款金额: %.2f 元\n", refund_amount);
    
    // 模拟退款处理
    printf("退款处理中");
    for (int i = 0; i < 3; i++) {
        printf(".");
        fflush(stdout);
    }
    printf("\n");
    
    // 更新订单状态
    order->payment_status = PAYMENT_REFUNDED;
    order->refund_amount = refund_amount;
    order->refunded_at = time(NULL);
    
    // 更新预约状态为已取消
    Reservation* reservation = get_reservation_by_id(order->reservation_id);
    if (reservation) {
        reservation->status = RESERVATION_CANCELLED;
        reservation->updated_at = time(NULL);
    }
    
    printf("退款成功！\n");
    printf("退款将在1-3个工作日内到账。\n");
    
    return 1;
}

int check_order_timeout(void) {
    time_t now = time(NULL);
    int timeout_count = 0;
    
    for (int i = 0; i < system_data.order_count; i++) {
        if (system_data.orders[i].payment_status == PAYMENT_PENDING && 
            now > system_data.orders[i].timeout_at) {
            
            system_data.orders[i].payment_status = PAYMENT_TIMEOUT;
            timeout_count++;
            
            // 可以选择是否自动取消对应的预约
            Reservation* reservation = get_reservation_by_id(system_data.orders[i].reservation_id);
            if (reservation && reservation->status == RESERVATION_PENDING) {
                // 保持预约状态，允许用户重新支付
                printf("订单 %s 已超时，预约保持待支付状态。\n", system_data.orders[i].order_no);
            }
        }
    }
    
    return timeout_count;
}

Order* get_order_by_id(int order_id) {
    for (int i = 0; i < system_data.order_count; i++) {
        if (system_data.orders[i].id == order_id) {
            return &system_data.orders[i];
        }
    }
    return NULL;
}

Order* get_order_by_reservation(int reservation_id) {
    for (int i = 0; i < system_data.order_count; i++) {
        if (system_data.orders[i].reservation_id == reservation_id) {
            return &system_data.orders[i];
        }
    }
    return NULL;
}

void show_order_details(int order_id) {
    Order* order = get_order_by_id(order_id);
    if (order == NULL) {
        printf("错误：订单不存在！\n");
        return;
    }
    
    // 获取预约信息
    Reservation* reservation = get_reservation_by_id(order->reservation_id);
    
    // 获取用户信息
    char username[MAX_NAME_LEN] = "未知用户";
    if (reservation) {
        for (int i = 0; i < system_data.user_count; i++) {
            if (system_data.users[i].id == reservation->user_id) {
                strcpy(username, system_data.users[i].real_name);
                break;
            }
        }
    }
    
    // 获取场地信息
    Venue* venue = NULL;
    if (reservation) {
        venue = get_venue_by_id(reservation->venue_id);
    }
    
    printf("\n==================== 订单详情 ====================\n");
    printf("订单ID：%d\n", order->id);
    printf("订单号：%s\n", order->order_no);
    printf("用户姓名：%s\n", username);
    
    if (reservation) {
        printf("预约ID：%d\n", reservation->id);
        printf("场地名称：%s\n", venue ? venue->name : "未知场地");
        
        char start_time[64], end_time[64];
        strftime(start_time, sizeof(start_time), "%Y-%m-%d %H:%M", localtime(&reservation->start_time));
        strftime(end_time, sizeof(end_time), "%Y-%m-%d %H:%M", localtime(&reservation->end_time));
        
        printf("预约时间：%s - %s\n", start_time, end_time);
    }
    
    printf("订单金额：%.2f 元\n", order->amount);
    printf("支付状态：%s\n", get_payment_status_string(order->payment_status));
    
    if (order->payment_status == PAYMENT_PAID) {
        printf("支付方式：%s\n", get_payment_method_string(order->payment_method));
        printf("交易号：%s\n", order->transaction_id);
        
        char paid_time[64];
        strftime(paid_time, sizeof(paid_time), "%Y-%m-%d %H:%M:%S", localtime(&order->paid_at));
        printf("支付时间：%s\n", paid_time);
    }
    
    if (order->payment_status == PAYMENT_REFUNDED) {
        printf("退款金额：%.2f 元\n", order->refund_amount);
        
        char refund_time[64];
        strftime(refund_time, sizeof(refund_time), "%Y-%m-%d %H:%M:%S", localtime(&order->refunded_at));
        printf("退款时间：%s\n", refund_time);
    }
    
    if (order->payment_status == PAYMENT_PENDING) {
        char timeout_time[64];
        strftime(timeout_time, sizeof(timeout_time), "%Y-%m-%d %H:%M:%S", localtime(&order->timeout_at));
        printf("超时时间：%s\n", timeout_time);
        
        time_t now = time(NULL);
        if (now < order->timeout_at) {
            int remaining_minutes = (int)((order->timeout_at - now) / 60);
            printf("剩余时间：%d 分钟\n", remaining_minutes);
        } else {
            printf("订单状态：已超时\n");
        }
    }
    
    char created_time[64];
    strftime(created_time, sizeof(created_time), "%Y-%m-%d %H:%M:%S", localtime(&order->created_at));
    printf("创建时间：%s\n", created_time);
    
    if (strlen(order->payment_desc) > 0) {
        printf("支付描述：%s\n", order->payment_desc);
    }
    
    printf("================================================\n");
}

const char* get_payment_status_string(PaymentStatus status) {
    switch (status) {
        case PAYMENT_PENDING: return "待支付";
        case PAYMENT_PAID: return "已支付";
        case PAYMENT_REFUNDED: return "已退款";
        case PAYMENT_FAILED: return "支付失败";
        case PAYMENT_CANCELLED: return "已取消";
        case PAYMENT_TIMEOUT: return "已超时";
        default: return "未知状态";
    }
}

const char* get_payment_method_string(PaymentMethod method) {
    switch (method) {
        case PAYMENT_METHOD_ALIPAY: return "支付宝";
        case PAYMENT_METHOD_WECHAT: return "微信支付";
        case PAYMENT_METHOD_CASH: return "现金支付";
        case PAYMENT_METHOD_CARD: return "银行卡";
        default: return "未知方式";
    }
}