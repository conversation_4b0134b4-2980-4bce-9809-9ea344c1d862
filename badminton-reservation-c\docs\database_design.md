# 羽毛球馆预约系统 - 数据库设计文档

## 概述

本文档描述了羽毛球馆预约管理系统的完整数据库设计，包括表结构、字段定义、约束条件和索引设计。

## 数据库表结构

### 1. 用户表 (users)

存储系统用户的基本信息和权限。

| 字段名 | 数据类型 | 长度 | 约束 | 描述 |
|--------|----------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 用户唯一标识 |
| username | VARCHAR | 50 | UNIQUE, NOT NULL | 用户名 |
| password | VARCHAR | 50 | NOT NULL | 密码 |
| email | VARCHAR | 100 | UNIQUE, NOT NULL | 邮箱地址 |
| phone | VARCHAR | 20 | UNIQUE, NOT NULL | 手机号码 |
| real_name | VARCHAR | 50 | NOT NULL | 真实姓名 |
| role | ENUM | - | NOT NULL | 用户角色：1-普通用户, 2-教练, 3-管理员, 4-超级管理员 |
| status | ENUM | - | NOT NULL | 用户状态：1-活跃, 2-非活跃, 3-已禁用 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计：**
- PRIMARY KEY (id)
- UNIQUE INDEX (username)
- UNIQUE INDEX (email)
- UNIQUE INDEX (phone)
- INDEX (role)
- INDEX (status)### 2. 场地表 (venues)

存储羽毛球场地的详细信息。

| 字段名 | 数据类型 | 长度 | 约束 | 描述 |
|--------|----------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 场地唯一标识 |
| name | VARCHAR | 50 | NOT NULL | 场地名称 |
| description | TEXT | 200 | - | 场地描述 |
| capacity | INT | - | NOT NULL | 容纳人数 |
| price_per_hour | DECIMAL | 10,2 | NOT NULL | 每小时价格 |
| equipment | TEXT | 200 | - | 设备配置 |
| status | ENUM | - | NOT NULL | 场地状态：1-可用, 2-维护中, 3-已禁用 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计：**
- PRIMARY KEY (id)
- INDEX (status)
- INDEX (price_per_hour)

### 3. 预约表 (reservations)

存储用户的场地预约记录。

| 字段名 | 数据类型 | 长度 | 约束 | 描述 |
|--------|----------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 预约唯一标识 |
| user_id | INT | - | NOT NULL, FOREIGN KEY | 用户ID |
| venue_id | INT | - | NOT NULL, FOREIGN KEY | 场地ID |
| start_time | TIMESTAMP | - | NOT NULL | 开始时间 |
| end_time | TIMESTAMP | - | NOT NULL | 结束时间 |
| total_amount | DECIMAL | 10,2 | NOT NULL | 总金额 |
| status | ENUM | - | NOT NULL | 预约状态：1-待支付, 2-已确认, 3-已完成, 4-已取消 |
| notes | TEXT | 200 | - | 备注信息 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |
| updated_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计：**
- PRIMARY KEY (id)
- FOREIGN KEY (user_id) REFERENCES users(id)
- FOREIGN KEY (venue_id) REFERENCES venues(id)
- INDEX (user_id)
- INDEX (venue_id)
- INDEX (start_time)
- INDEX (end_time)
- INDEX (status)
- COMPOSITE INDEX (venue_id, start_time, end_time) -- 用于时间冲突检测### 4. 订单表 (orders)

存储支付订单信息。

| 字段名 | 数据类型 | 长度 | 约束 | 描述 |
|--------|----------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 订单唯一标识 |
| reservation_id | INT | - | NOT NULL, FOREIGN KEY | 预约ID |
| order_no | VARCHAR | 32 | UNIQUE, NOT NULL | 订单号 |
| amount | DECIMAL | 10,2 | NOT NULL | 订单金额 |
| payment_status | ENUM | - | NOT NULL | 支付状态：1-待支付, 2-已支付, 3-已退款, 4-支付失败 |
| paid_at | TIMESTAMP | - | NULL | 支付时间 |
| created_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP | 创建时间 |

**索引设计：**
- PRIMARY KEY (id)
- UNIQUE INDEX (order_no)
- FOREIGN KEY (reservation_id) REFERENCES reservations(id)
- INDEX (reservation_id)
- INDEX (payment_status)
- INDEX (created_at)

### 5. 系统配置表 (system_config)

存储系统配置参数。

| 字段名 | 数据类型 | 长度 | 约束 | 描述 |
|--------|----------|------|------|------|
| id | INT | - | PRIMARY KEY, AUTO_INCREMENT | 配置ID |
| config_key | VARCHAR | 50 | UNIQUE, NOT NULL | 配置键 |
| config_value | TEXT | - | NOT NULL | 配置值 |
| description | VARCHAR | 200 | - | 配置描述 |
| updated_at | TIMESTAMP | - | DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP | 更新时间 |

**索引设计：**
- PRIMARY KEY (id)
- UNIQUE INDEX (config_key)

## 数据关系图

```
users (1) -----> (N) reservations (1) -----> (1) orders
venues (1) -----> (N) reservations
```

## C语言实现的数据结构

在我们的C语言实现中，使用结构体来模拟数据库表：

```c
// 用户结构体
typedef struct {
    int id;
    char username[MAX_NAME_LEN];
    char password[MAX_PASSWORD_LEN];
    char email[MAX_EMAIL_LEN];
    char phone[MAX_PHONE_LEN];
    char real_name[MAX_NAME_LEN];
    UserRole role;
    UserStatus status;
    time_t created_at;
    time_t updated_at;
} User;

// 场地结构体
typedef struct {
    int id;
    char name[MAX_NAME_LEN];
    char description[MAX_DESC_LEN];
    int capacity;
    double price_per_hour;
    char equipment[MAX_DESC_LEN];
    VenueStatus status;
    time_t created_at;
    time_t updated_at;
} Venue;

// 预约结构体
typedef struct {
    int id;
    int user_id;
    int venue_id;
    time_t start_time;
    time_t end_time;
    double total_amount;
    ReservationStatus status;
    char notes[MAX_DESC_LEN];
    time_t created_at;
    time_t updated_at;
} Reservation;

// 订单结构体
typedef struct {
    int id;
    int reservation_id;
    char order_no[32];
    double amount;
    PaymentStatus payment_status;
    time_t paid_at;
    time_t created_at;
} Order;
```## 业务约束

### 1. 用户约束
- 用户名、邮箱、手机号必须唯一
- 密码长度至少6位
- 邮箱格式验证
- 手机号格式验证（11位数字）

### 2. 场地约束
- 场地名称不能为空
- 价格必须大于0
- 容纳人数必须大于0

### 3. 预约约束
- 开始时间必须小于结束时间
- 预约时间不能与同场地其他预约冲突
- 预约时间不能早于当前时间
- 总金额必须大于0

### 4. 订单约束
- 订单号必须唯一
- 订单金额必须大于0
- 每个预约只能有一个订单

## 性能优化

### 1. 索引策略
- 为所有外键创建索引
- 为查询频繁的字段创建索引
- 为时间冲突检测创建复合索引

### 2. 查询优化
- 使用复合索引优化时间范围查询
- 分页查询避免全表扫描
- 使用适当的JOIN类型

### 3. 数据分区
- 可考虑按时间对预约表进行分区
- 历史数据归档策略

## 数据完整性

### 1. 参照完整性
- 预约表的user_id必须存在于用户表
- 预约表的venue_id必须存在于场地表
- 订单表的reservation_id必须存在于预约表

### 2. 业务完整性
- 预约时间不能冲突
- 订单状态与预约状态保持一致
- 用户权限验证

## 安全考虑

### 1. 数据加密
- 密码使用哈希加密存储
- 敏感信息传输加密

### 2. 访问控制
- 基于角色的权限控制
- 数据访问日志记录

### 3. 数据备份
- 定期数据备份
- 灾难恢复方案

## 扩展性设计

### 1. 水平扩展
- 支持读写分离
- 支持分库分表

### 2. 功能扩展
- 预留扩展字段
- 支持插件化功能

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-06-26 | 初始版本，完成基础表结构设计 | Alex |

## 总结

本数据库设计满足羽毛球馆预约系统的所有业务需求，具备良好的扩展性和性能。通过合理的索引设计和约束条件，确保数据的完整性和查询效率。在C语言实现中，我们使用结构体和枚举类型来模拟数据库表结构，实现了完整的数据模型和业务逻辑。