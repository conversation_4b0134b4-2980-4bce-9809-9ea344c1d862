# 羽毛球馆预约系统 - 预约管理核心功能文档

## 概述

本文档描述了羽毛球馆预约管理系统的预约管理核心功能的完整实现，包括预约创建、时间冲突检测、预约状态管理、预约历史查询、预约取消等核心功能。

## 系统架构

### 1. 预约管理架构设计

```
预约管理系统
    |
    ├── 预约创建流程
    │   ├── 时间有效性验证
    │   ├── 场地可用性检查
    │   ├── 时间冲突检测
    │   ├── 费用计算
    │   └── 预约记录创建
    |
    ├── 预约状态管理
    │   ├── 待支付状态
    │   ├── 已确认状态
    │   ├── 已完成状态
    │   └── 已取消状态
    |
    ├── 预约查询功能
    │   ├── 用户预约历史
    │   ├── 管理员全局查询
    │   ├── 预约详情查看
    │   └── 预约统计分析
    |
    └── 预约操作管理
        ├── 预约取消
        ├── 预约修改
        ├── 预约确认
        └── 预约完成
```

### 2. 数据模型设计

**预约数据结构：**
```c
typedef struct {
    int id;                           // 预约唯一标识
    int user_id;                      // 用户ID
    int venue_id;                     // 场地ID
    time_t start_time;                // 开始时间
    time_t end_time;                  // 结束时间
    double total_amount;              // 总金额
    ReservationStatus status;         // 预约状态
    char notes[MAX_DESC_LEN];         // 备注信息
    time_t created_at;                // 创建时间
    time_t updated_at;                // 更新时间
} Reservation;
```

**预约状态枚举：**
```c
typedef enum {
    RESERVATION_PENDING = 1,    // 待支付状态
    RESERVATION_CONFIRMED = 2,  // 已确认状态
    RESERVATION_COMPLETED = 3,  // 已完成状态
    RESERVATION_CANCELLED = 4   // 已取消状态
} ReservationStatus;
```

## 核心功能实现

### 1. 预约创建功能

**函数签名：**
```c
int create_reservation(int user_id, int venue_id, time_t start_time, 
                      time_t end_time, const char* notes);
```

**功能特性：**
- ✅ 预约数量限制检查（MAX_RESERVATIONS = 200）
- ✅ 场地存在性验证
- ✅ 场地可用性检查
- ✅ 时间有效性验证
- ✅ 时间冲突检测
- ✅ 自动费用计算
- ✅ 预约记录创建
- ✅ 详细反馈信息

**验证规则：**
- 开始时间必须早于结束时间
- 不能预约过去的时间
- 场地必须处于可用状态
- 时间段不能与现有预约冲突
- 用户必须存在且有效

**实现逻辑：**
```c
int create_reservation(int user_id, int venue_id, time_t start_time, 
                      time_t end_time, const char* notes) {
    // 1. 基础验证
    if (system_data.reservation_count >= MAX_RESERVATIONS) {
        printf("错误：预约数量已达上限！\n");
        return 0;
    }
    
    // 2. 场地验证
    Venue* venue = get_venue_by_id(venue_id);
    if (venue == NULL || venue->status != VENUE_AVAILABLE) {
        printf("错误：场地不可用！\n");
        return 0;
    }
    
    // 3. 时间验证
    if (start_time >= end_time || start_time <= time(NULL)) {
        printf("错误：时间设置无效！\n");
        return 0;
    }
    
    // 4. 冲突检测
    if (!check_venue_availability(venue_id, start_time, end_time)) {
        printf("错误：时间段已被预约！\n");
        return 0;
    }
    
    // 5. 创建预约
    // ... 预约创建逻辑
}
```

### 2. 时间冲突检测功能

**函数签名：**
```c
int check_venue_availability(int venue_id, time_t start_time, time_t end_time);
```

**功能特性：**
- ✅ 精确的时间冲突算法
- ✅ 多预约状态考虑（待支付、已确认）
- ✅ 高效的线性搜索
- ✅ 边界条件处理

**冲突检测算法：**
```c
// 时间冲突检测逻辑
if ((start_time >= existing_start && start_time < existing_end) ||      // 开始时间冲突
    (end_time > existing_start && end_time <= existing_end) ||          // 结束时间冲突
    (start_time <= existing_start && end_time >= existing_end)) {       // 完全包含
    return 0; // 时间冲突
}
```

**冲突场景分析：**
1. **部分重叠**：新预约的开始或结束时间与现有预约重叠
2. **完全包含**：新预约完全包含现有预约时间段
3. **被包含**：新预约被现有预约时间段完全包含
4. **边界接触**：时间点相邻但不重叠（允许）

### 3. 预约状态管理

**状态流转图：**
```
PENDING (待支付) → CONFIRMED (已确认) → COMPLETED (已完成)
       ↓                    ↓
   CANCELLED (已取消)   CANCELLED (已取消)
```

**状态转换规则：**
- 新建预约默认为待支付状态
- 支付完成后转为已确认状态
- 预约时间结束后转为已完成状态
- 任何时候都可以取消（有时间限制）

**状态显示映射：**
```c
const char* get_status_string(ReservationStatus status) {
    switch (status) {
        case RESERVATION_PENDING: return "待支付";
        case RESERVATION_CONFIRMED: return "已确认";
        case RESERVATION_COMPLETED: return "已完成";
        case RESERVATION_CANCELLED: return "已取消";
        default: return "未知";
    }
}
```

### 4. 预约取消功能

**函数签名：**
```c
int cancel_reservation(int reservation_id);
```

**功能特性：**
- ✅ 预约存在性检查
- ✅ 状态有效性验证
- ✅ 时间限制检查（提前2小时）
- ✅ 状态更新和时间戳记录
- ✅ 完整的错误处理

**取消规则：**
- 已取消的预约不能再次取消
- 已完成的预约不能取消
- 距离预约时间不足2小时不能取消
- 取消后状态更新为已取消

**实现逻辑：**
```c
int cancel_reservation(int reservation_id) {
    // 1. 查找预约
    Reservation* reservation = find_reservation_by_id(reservation_id);
    if (!reservation) return 0;
    
    // 2. 状态检查
    if (reservation->status == RESERVATION_CANCELLED || 
        reservation->status == RESERVATION_COMPLETED) {
        return 0;
    }
    
    // 3. 时间限制检查
    if (difftime(reservation->start_time, time(NULL)) < 2 * 3600) {
        printf("错误：距离预约时间不足2小时，无法取消！\n");
        return 0;
    }
    
    // 4. 执行取消
    reservation->status = RESERVATION_CANCELLED;
    reservation->updated_at = time(NULL);
    return 1;
}
```

### 5. 预约查询功能

**用户预约查询：**
```c
void list_user_reservations(int user_id);
```

**管理员全局查询：**
```c
void list_all_reservations(void);
```

**功能特性：**
- ✅ 格式化表格显示
- ✅ 多字段信息展示
- ✅ 状态中文显示
- ✅ 时间格式化
- ✅ 用户权限控制

**显示格式：**
```
================= 我的预约列表 =================
ID    场地          开始时间            结束时间            状态      金额
================================================================
1     1号场地       2025-06-27 09:00    2025-06-27 11:00    已确认    120.00
2     2号场地       2025-06-28 14:00    2025-06-28 16:00    待支付    160.00
================================================================
```

## 业务逻辑设计

### 1. 费用计算逻辑

**计算公式：**
```c
double calculate_reservation_cost(time_t start_time, time_t end_time, double price_per_hour) {
    double hours = difftime(end_time, start_time) / 3600.0;
    return hours * price_per_hour;
}
```

**费用规则：**
- 按小时计费，不足1小时按1小时计算
- 价格基于场地的每小时价格
- 支持小数点后2位精度
- 自动四舍五入处理

### 2. 时间管理规则

**营业时间限制：**
- 营业时间：08:00 - 22:00
- 最小预约时长：1小时
- 最大预约时长：4小时
- 预约间隔：30分钟

**时间验证逻辑：**
```c
int validate_reservation_time(time_t start_time, time_t end_time) {
    struct tm* start_tm = localtime(&start_time);
    struct tm* end_tm = localtime(&end_time);
    
    // 检查营业时间
    if (start_tm->tm_hour < 8 || end_tm->tm_hour > 22) {
        return 0;
    }
    
    // 检查预约时长
    double hours = difftime(end_time, start_time) / 3600.0;
    if (hours < 1.0 || hours > 4.0) {
        return 0;
    }
    
    return 1;
}
```

### 3. 并发控制机制

**简单锁机制：**
虽然C语言实现中没有真正的分布式锁，但通过以下方式保证数据一致性：

- 原子性操作：预约创建过程中的所有检查和创建操作
- 状态检查：在修改前重新检查预约状态
- 时间戳验证：通过更新时间戳检测并发修改

**伪代码示例：**
```c
int create_reservation_with_lock(/* parameters */) {
    // 1. 获取当前时间戳作为操作标识
    time_t operation_time = time(NULL);
    
    // 2. 执行所有验证
    if (!validate_all_conditions()) {
        return 0;
    }
    
    // 3. 再次检查冲突（防止并发创建）
    if (!check_venue_availability(venue_id, start_time, end_time)) {
        return 0;
    }
    
    // 4. 原子性创建预约
    return create_reservation_atomic(/* parameters */);
}
```

## 用户界面设计

### 1. 预约创建界面

**输入字段：**
- 场地选择（下拉列表）
- 预约日期（日期选择器）
- 开始时间（时间选择器）
- 结束时间（时间选择器）
- 备注信息（文本框）

**验证提示：**
- 实时时间冲突检查
- 场地可用性提示
- 费用实时计算显示
- 错误信息友好提示

### 2. 预约列表界面

**显示内容：**
- 预约基本信息（ID、场地、时间）
- 预约状态（带颜色标识）
- 费用信息
- 操作按钮（取消、查看详情）

**筛选功能：**
- 按状态筛选
- 按时间范围筛选
- 按场地筛选

### 3. 预约详情界面

**详细信息：**
- 预约完整信息
- 场地详细信息
- 用户信息
- 创建和更新时间
- 操作历史记录

## 权限控制

### 1. 用户权限

**可执行操作：**
- ✅ 创建自己的预约
- ✅ 查看自己的预约历史
- ✅ 取消自己的预约
- ✅ 查看预约详情
- ❌ 查看他人预约
- ❌ 修改他人预约

### 2. 管理员权限

**可执行操作：**
- ✅ 查看所有预约
- ✅ 取消任何预约
- ✅ 修改预约状态
- ✅ 查看预约统计
- ✅ 导出预约数据

### 3. 权限检查机制

**实现方式：**
```c
int check_reservation_permission(int user_id, int reservation_id, const char* operation) {
    // 管理员有所有权限
    if (current_user->role >= ROLE_ADMIN) {
        return 1;
    }
    
    // 普通用户只能操作自己的预约
    Reservation* reservation = find_reservation_by_id(reservation_id);
    if (reservation && reservation->user_id == user_id) {
        return 1;
    }
    
    return 0;
}
```## 数据完整性保护

### 1. 引用完整性

**用户关联检查：**
- 预约创建时验证用户存在性
- 用户删除前检查是否有未完成预约
- 预约查询时验证用户权限

**场地关联检查：**
- 预约创建时验证场地存在性和可用性
- 场地删除前检查是否有未完成预约
- 场地状态变更时处理相关预约

**实现机制：**
```c
int validate_reservation_references(int user_id, int venue_id) {
    // 检查用户是否存在且有效
    User* user = get_user_by_id(user_id);
    if (!user || user->status != STATUS_ACTIVE) {
        return 0;
    }
    
    // 检查场地是否存在且可用
    Venue* venue = get_venue_by_id(venue_id);
    if (!venue || venue->status != VENUE_AVAILABLE) {
        return 0;
    }
    
    return 1;
}
```

### 2. 数据一致性

**状态一致性：**
- 预约状态与时间的一致性
- 费用与时长的一致性
- 更新时间戳的准确性

**业务规则一致性：**
- 时间冲突的严格检查
- 取消规则的一致执行
- 权限控制的统一应用

### 3. 事务性操作

**原子性保证：**
- 预约创建的完整性
- 状态变更的一致性
- 错误回滚机制

## 性能优化

### 1. 查询优化

**当前实现：**
- 线性搜索预约数组
- O(n)时间复杂度

**优化建议：**
- 按时间排序的预约列表
- 时间段索引优化
- 用户预约索引

**优化示例：**
```c
// 时间段索引结构
typedef struct {
    time_t time_slot;
    int reservation_ids[MAX_CONCURRENT_RESERVATIONS];
    int count;
} TimeSlotIndex;

// 优化的冲突检测
int check_venue_availability_optimized(int venue_id, time_t start_time, time_t end_time) {
    // 使用时间段索引快速查找可能冲突的预约
    TimeSlotIndex* slots = get_time_slots(start_time, end_time);
    
    for (int i = 0; i < slots->count; i++) {
        Reservation* res = get_reservation_by_id(slots->reservation_ids[i]);
        if (res && res->venue_id == venue_id && 
            (res->status == RESERVATION_PENDING || res->status == RESERVATION_CONFIRMED)) {
            if (has_time_conflict(start_time, end_time, res->start_time, res->end_time)) {
                return 0;
            }
        }
    }
    
    return 1;
}
```

### 2. 内存优化

**优化策略：**
- 合理设置MAX_RESERVATIONS常量
- 优化字符串存储
- 减少内存碎片
- 预约记录压缩存储

### 3. 缓存策略

**缓存机制：**
- 热门时间段缓存
- 用户预约历史缓存
- 场地可用性缓存
- 查询结果缓存

## 扩展功能

### 1. 预约类型管理

**功能设计：**
```c
typedef enum {
    RESERVATION_TYPE_NORMAL = 1,    // 普通预约
    RESERVATION_TYPE_TRAINING = 2,  // 训练预约
    RESERVATION_TYPE_MATCH = 3,     // 比赛预约
    RESERVATION_TYPE_COURSE = 4     // 课程预约
} ReservationType;

typedef struct {
    int id;
    ReservationType type;
    char type_name[50];
    double price_multiplier;        // 价格倍数
    int min_duration;              // 最小时长（分钟）
    int max_duration;              // 最大时长（分钟）
    int advance_booking_days;      // 提前预约天数
} ReservationTypeConfig;
```

### 2. 重复预约功能

**功能设计：**
```c
typedef struct {
    int id;
    int user_id;
    int venue_id;
    time_t start_time;
    time_t end_time;
    int repeat_type;               // 重复类型：1-每天，2-每周，3-每月
    int repeat_count;              // 重复次数
    time_t repeat_end_date;        // 重复结束日期
    int created_reservations[50];  // 已创建的预约ID列表
    int reservation_count;
} RecurringReservation;
```

### 3. 预约提醒功能

**功能设计：**
```c
typedef struct {
    int reservation_id;
    int user_id;
    time_t remind_time;
    int remind_type;               // 提醒类型：1-短信，2-邮件，3-系统通知
    int is_sent;                   // 是否已发送
    char message[200];             // 提醒消息
} ReservationReminder;
```

### 4. 预约评价系统

**功能设计：**
```c
typedef struct {
    int reservation_id;
    int user_id;
    int venue_id;
    int rating;                    // 1-5星评分
    char comment[200];             // 评价内容
    time_t created_at;
} ReservationReview;
```

## 测试用例

### 1. 预约创建测试

**测试场景：**
```c
// 正常预约创建
time_t start = time(NULL) + 24 * 3600;  // 明天
time_t end = start + 2 * 3600;          // 2小时后
create_reservation(1, 1, start, end, "测试预约");

// 时间冲突测试
create_reservation(2, 1, start + 3600, end + 3600, "冲突预约");

// 无效时间测试
create_reservation(1, 1, end, start, "无效时间");  // 结束时间早于开始时间

// 过去时间测试
time_t past = time(NULL) - 3600;
create_reservation(1, 1, past, past + 3600, "过去时间");

// 场地不可用测试
set_venue_status(1, VENUE_MAINTENANCE);
create_reservation(1, 1, start, end, "场地维护中");
```

### 2. 时间冲突检测测试

**测试场景：**
```c
// 部分重叠测试
time_t base_start = time(NULL) + 24 * 3600;
time_t base_end = base_start + 2 * 3600;

// 创建基础预约
create_reservation(1, 1, base_start, base_end, "基础预约");

// 测试各种冲突情况
check_venue_availability(1, base_start - 1800, base_start + 1800);  // 开始时间重叠
check_venue_availability(1, base_end - 1800, base_end + 1800);      // 结束时间重叠
check_venue_availability(1, base_start - 1800, base_end + 1800);    // 完全包含
check_venue_availability(1, base_start + 1800, base_end - 1800);    // 被包含
```

### 3. 预约取消测试

**测试场景：**
```c
// 正常取消测试
time_t future_start = time(NULL) + 24 * 3600;
int reservation_id = create_reservation(1, 1, future_start, future_start + 3600, "测试");
cancel_reservation(reservation_id);

// 时间限制测试
time_t near_start = time(NULL) + 3600;  // 1小时后
int near_id = create_reservation(1, 1, near_start, near_start + 3600, "近期预约");
cancel_reservation(near_id);  // 应该失败

// 重复取消测试
cancel_reservation(reservation_id);  // 应该失败

// 不存在预约测试
cancel_reservation(999);  // 应该失败
```

### 4. 预约查询测试

**测试场景：**
```c
// 用户预约查询
list_user_reservations(1);

// 管理员全局查询
if (current_user->role >= ROLE_ADMIN) {
    list_all_reservations();
}

// 空结果测试
list_user_reservations(999);  // 不存在的用户
```

## 错误处理

### 1. 输入验证错误

**错误类型：**
- 无效的用户ID或场地ID
- 无效的时间参数
- 超长的备注信息
- 空指针参数

**处理策略：**
```c
int validate_reservation_input(int user_id, int venue_id, time_t start_time, 
                              time_t end_time, const char* notes) {
    if (user_id <= 0 || venue_id <= 0) {
        printf("错误：无效的用户ID或场地ID！\n");
        return 0;
    }
    
    if (start_time <= 0 || end_time <= 0) {
        printf("错误：无效的时间参数！\n");
        return 0;
    }
    
    if (notes && strlen(notes) > MAX_DESC_LEN) {
        printf("错误：备注信息过长！\n");
        return 0;
    }
    
    return 1;
}
```

### 2. 业务逻辑错误

**错误类型：**
- 时间冲突
- 场地不可用
- 权限不足
- 状态转换错误

**处理策略：**
- 详细的错误信息提示
- 建议性的解决方案
- 友好的用户体验

### 3. 系统错误

**错误类型：**
- 内存不足
- 数据损坏
- 并发冲突

**处理策略：**
- 异常捕获和恢复
- 数据备份和恢复
- 错误日志记录

## 部署配置

### 1. 系统配置

**配置参数：**
```c
#define MAX_RESERVATIONS 200           // 最大预约数量
#define MIN_RESERVATION_HOURS 1        // 最小预约时长
#define MAX_RESERVATION_HOURS 4        // 最大预约时长
#define CANCEL_TIME_LIMIT 2            // 取消时间限制（小时）
#define BUSINESS_START_HOUR 8          // 营业开始时间
#define BUSINESS_END_HOUR 22           // 营业结束时间
```

### 2. 数据初始化

**测试数据：**
```c
void init_test_reservations() {
    time_t tomorrow = time(NULL) + 24 * 3600;
    
    // 创建测试预约
    create_reservation(1, 1, tomorrow + 9 * 3600, tomorrow + 11 * 3600, "测试预约1");
    create_reservation(2, 2, tomorrow + 14 * 3600, tomorrow + 16 * 3600, "测试预约2");
    create_reservation(1, 3, tomorrow + 19 * 3600, tomorrow + 21 * 3600, "测试预约3");
}
```

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-06-26 | 初始版本，完成基础预约管理功能 | Alex |

## 总结

本预约管理系统实现了完整的预约生命周期管理功能，包括创建、查询、取消等核心操作。通过精确的时间冲突检测算法和完善的业务逻辑，确保了预约数据的准确性和系统的稳定性。

系统支持多种预约状态管理，提供了完整的权限控制机制，能够满足羽毛球馆的日常预约需求。通过优化的查询算法和缓存策略，保证了系统的性能和响应速度。

扩展功能设计为系统的未来发展提供了良好的基础，包括预约类型管理、重复预约、提醒功能和评价系统等高级功能。完整的测试用例和错误处理机制确保了系统的可靠性和用户体验。

预约管理作为羽毛球馆系统的核心功能，通过合理的架构设计和严格的业务规则实现，为用户提供了便捷、可靠的预约服务体验。