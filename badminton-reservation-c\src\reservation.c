#include "../include/badminton.h"

int check_venue_availability(int venue_id, time_t start_time, time_t end_time) {
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].venue_id == venue_id && 
            (system_data.reservations[i].status == RESERVATION_PENDING || 
             system_data.reservations[i].status == RESERVATION_CONFIRMED)) {
            
            // 检查时间冲突
            if ((start_time >= system_data.reservations[i].start_time && start_time < system_data.reservations[i].end_time) ||
                (end_time > system_data.reservations[i].start_time && end_time <= system_data.reservations[i].end_time) ||
                (start_time <= system_data.reservations[i].start_time && end_time >= system_data.reservations[i].end_time)) {
                return 0; // 时间冲突
            }
        }
    }
    return 1; // 可用
}

int create_reservation(int user_id, int venue_id, time_t start_time, time_t end_time, const char* notes) {
    if (system_data.reservation_count >= MAX_RESERVATIONS) {
        printf("错误：预约数量已达上限！\n");
        return 0;
    }
    
    // 检查场地是否存在
    Venue* venue = get_venue_by_id(venue_id);
    if (venue == NULL) {
        printf("错误：场地不存在！\n");
        return 0;
    }
    
    // 检查场地是否可用
    if (venue->status != VENUE_AVAILABLE) {
        printf("错误：场地当前不可用！\n");
        return 0;
    }
    
    // 检查时间有效性
    if (start_time >= end_time) {
        printf("错误：开始时间必须早于结束时间！\n");
        return 0;
    }
    
    // 检查是否提前预约
    time_t now = time(NULL);
    if (start_time <= now) {
        printf("错误：不能预约过去的时间！\n");
        return 0;
    }
    
    // 检查场地可用性
    if (!check_venue_availability(venue_id, start_time, end_time)) {
        printf("错误：该时间段场地已被预约！\n");
        return 0;
    }
    
    // 计算费用
    double hours = difftime(end_time, start_time) / 3600.0;
    double total_amount = hours * venue->price_per_hour;
    
    // 创建预约
    Reservation* new_reservation = &system_data.reservations[system_data.reservation_count];
    new_reservation->id = system_data.next_reservation_id++;
    new_reservation->user_id = user_id;
    new_reservation->venue_id = venue_id;
    new_reservation->start_time = start_time;
    new_reservation->end_time = end_time;
    new_reservation->total_amount = total_amount;
    new_reservation->status = RESERVATION_PENDING;
    strcpy(new_reservation->notes, notes);
    new_reservation->created_at = time(NULL);
    new_reservation->updated_at = time(NULL);
    
    system_data.reservation_count++;
    
    printf("预约创建成功！\n");
    printf("预约ID: %d\n", new_reservation->id);
    printf("场地: %s\n", venue->name);
    printf("时间: %s - %s\n", format_time(start_time), format_time(end_time));
    printf("费用: %.2f 元\n", total_amount);
    
    return new_reservation->id;
}int cancel_reservation(int reservation_id) {
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].id == reservation_id) {
            if (system_data.reservations[i].status == RESERVATION_CANCELLED) {
                printf("错误：预约已经被取消！\n");
                return 0;
            }
            
            if (system_data.reservations[i].status == RESERVATION_COMPLETED) {
                printf("错误：已完成的预约无法取消！\n");
                return 0;
            }
            
            // 检查取消时间限制（提前2小时）
            time_t now = time(NULL);
            if (difftime(system_data.reservations[i].start_time, now) < 2 * 3600) {
                printf("错误：距离预约时间不足2小时，无法取消！\n");
                return 0;
            }
            
            system_data.reservations[i].status = RESERVATION_CANCELLED;
            system_data.reservations[i].updated_at = time(NULL);
            
            printf("预约取消成功！\n");
            return 1;
        }
    }
    printf("错误：预约不存在！\n");
    return 0;
}

void list_user_reservations(int user_id) {
    printf("\n================= 我的预约列表 =================\n");
    printf("ID\t场地\t\t开始时间\t\t结束时间\t\t状态\t\t金额\n");
    printf("================================================================\n");
    
    int found = 0;
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].user_id == user_id) {
            found = 1;
            
            Venue* venue = get_venue_by_id(system_data.reservations[i].venue_id);
            const char* status_str;
            switch (system_data.reservations[i].status) {
                case RESERVATION_PENDING: status_str = "待支付"; break;
                case RESERVATION_CONFIRMED: status_str = "已确认"; break;
                case RESERVATION_COMPLETED: status_str = "已完成"; break;
                case RESERVATION_CANCELLED: status_str = "已取消"; break;
                default: status_str = "未知"; break;
            }
            
            printf("%d\t%-12s\t%s\t%s\t%-8s\t%.2f\n",
                   system_data.reservations[i].id,
                   venue ? venue->name : "未知场地",
                   format_time(system_data.reservations[i].start_time),
                   format_time(system_data.reservations[i].end_time),
                   status_str,
                   system_data.reservations[i].total_amount);
        }
    }
    
    if (!found) {
        printf("暂无预约记录\n");
    }
    printf("================================================================\n");
}

void list_all_reservations(void) {
    printf("\n================= 所有预约列表 =================\n");
    printf("ID\t用户\t\t场地\t\t开始时间\t\t结束时间\t\t状态\t\t金额\n");
    printf("========================================================================\n");
    
    for (int i = 0; i < system_data.reservation_count; i++) {
        Venue* venue = get_venue_by_id(system_data.reservations[i].venue_id);
        
        // 查找用户名
        char username[MAX_NAME_LEN] = "未知用户";
        for (int j = 0; j < system_data.user_count; j++) {
            if (system_data.users[j].id == system_data.reservations[i].user_id) {
                strcpy(username, system_data.users[j].real_name);
                break;
            }
        }
        
        const char* status_str;
        switch (system_data.reservations[i].status) {
            case RESERVATION_PENDING: status_str = "待支付"; break;
            case RESERVATION_CONFIRMED: status_str = "已确认"; break;
            case RESERVATION_COMPLETED: status_str = "已完成"; break;
            case RESERVATION_CANCELLED: status_str = "已取消"; break;
            default: status_str = "未知"; break;
        }
        
        printf("%d\t%-12s\t%-12s\t%s\t%s\t%-8s\t%.2f\n",
               system_data.reservations[i].id,
               username,
               venue ? venue->name : "未知场地",
               format_time(system_data.reservations[i].start_time),
               format_time(system_data.reservations[i].end_time),
               status_str,
               system_data.reservations[i].total_amount);
    }
    printf("========================================================================\n");
}int confirm_reservation(int reservation_id) {
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].id == reservation_id) {
            if (system_data.reservations[i].status != RESERVATION_PENDING) {
                printf("错误：只有待支付的预约才能确认！\n");
                return 0;
            }
            
            system_data.reservations[i].status = RESERVATION_CONFIRMED;
            system_data.reservations[i].updated_at = time(NULL);
            
            printf("预约确认成功！\n");
            return 1;
        }
    }
    printf("错误：预约不存在！\n");
    return 0;
}

int complete_reservation(int reservation_id) {
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].id == reservation_id) {
            if (system_data.reservations[i].status != RESERVATION_CONFIRMED) {
                printf("错误：只有已确认的预约才能完成！\n");
                return 0;
            }
            
            // 检查是否到了预约时间
            time_t now = time(NULL);
            if (now < system_data.reservations[i].end_time) {
                printf("警告：预约时间尚未结束，确定要完成吗？(y/n): ");
                char confirm;
                scanf(" %c", &confirm);
                if (confirm != 'y' && confirm != 'Y') {
                    printf("操作已取消。\n");
                    return 0;
                }
            }
            
            system_data.reservations[i].status = RESERVATION_COMPLETED;
            system_data.reservations[i].updated_at = time(NULL);
            
            printf("预约完成成功！\n");
            return 1;
        }
    }
    printf("错误：预约不存在！\n");
    return 0;
}

Reservation* get_reservation_by_id(int reservation_id) {
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].id == reservation_id) {
            return &system_data.reservations[i];
        }
    }
    return NULL;
}

void show_reservation_details(int reservation_id) {
    Reservation* reservation = get_reservation_by_id(reservation_id);
    if (reservation == NULL) {
        printf("错误：预约不存在！\n");
        return;
    }
    
    // 获取用户信息
    char username[MAX_NAME_LEN] = "未知用户";
    char user_phone[MAX_PHONE_LEN] = "未知";
    for (int i = 0; i < system_data.user_count; i++) {
        if (system_data.users[i].id == reservation->user_id) {
            strcpy(username, system_data.users[i].real_name);
            strcpy(user_phone, system_data.users[i].phone);
            break;
        }
    }
    
    // 获取场地信息
    Venue* venue = get_venue_by_id(reservation->venue_id);
    
    printf("\n==================== 预约详情 ====================\n");
    printf("预约ID：%d\n", reservation->id);
    printf("用户姓名：%s\n", username);
    printf("联系电话：%s\n", user_phone);
    printf("场地名称：%s\n", venue ? venue->name : "未知场地");
    printf("场地描述：%s\n", venue ? venue->description : "无描述");
    
    char start_time[64], end_time[64];
    strftime(start_time, sizeof(start_time), "%Y-%m-%d %H:%M:%S", localtime(&reservation->start_time));
    strftime(end_time, sizeof(end_time), "%Y-%m-%d %H:%M:%S", localtime(&reservation->end_time));
    
    printf("开始时间：%s\n", start_time);
    printf("结束时间：%s\n", end_time);
    
    double hours = difftime(reservation->end_time, reservation->start_time) / 3600.0;
    printf("预约时长：%.1f 小时\n", hours);
    printf("总费用：%.2f 元\n", reservation->total_amount);
    
    const char* status_str;
    switch (reservation->status) {
        case RESERVATION_PENDING: status_str = "待支付"; break;
        case RESERVATION_CONFIRMED: status_str = "已确认"; break;
        case RESERVATION_COMPLETED: status_str = "已完成"; break;
        case RESERVATION_CANCELLED: status_str = "已取消"; break;
        default: status_str = "未知"; break;
    }
    printf("预约状态：%s\n", status_str);
    
    if (strlen(reservation->notes) > 0) {
        printf("备注信息：%s\n", reservation->notes);
    }
    
    char created_time[64], updated_time[64];
    strftime(created_time, sizeof(created_time), "%Y-%m-%d %H:%M:%S", localtime(&reservation->created_at));
    strftime(updated_time, sizeof(updated_time), "%Y-%m-%d %H:%M:%S", localtime(&reservation->updated_at));
    
    printf("创建时间：%s\n", created_time);
    printf("更新时间：%s\n", updated_time);
    
    // 显示可执行的操作
    printf("\n可执行操作：\n");
    if (reservation->status == RESERVATION_PENDING) {
        printf("- 确认预约（支付后）\n");
        printf("- 取消预约\n");
    } else if (reservation->status == RESERVATION_CONFIRMED) {
        time_t now = time(NULL);
        if (difftime(reservation->start_time, now) >= 2 * 3600) {
            printf("- 取消预约\n");
        }
        if (now >= reservation->end_time) {
            printf("- 完成预约\n");
        }
    }
    
    printf("================================================\n");
}