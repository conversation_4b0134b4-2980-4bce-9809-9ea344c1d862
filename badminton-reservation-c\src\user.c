#include "../include/badminton.h"

int register_user(const char* username, const char* password, const char* email, const char* phone, const char* real_name) {
    // 检查用户名是否已存在
    for (int i = 0; i < system_data.user_count; i++) {
        if (strcmp(system_data.users[i].username, username) == 0) {
            printf("错误：用户名已存在！\n");
            return 0;
        }
    }
    
    // 检查是否超过最大用户数
    if (system_data.user_count >= MAX_USERS) {
        printf("错误：用户数量已达上限！\n");
        return 0;
    }
    
    // 验证邮箱格式
    if (!validate_email(email)) {
        printf("错误：邮箱格式不正确！\n");
        return 0;
    }
    
    // 验证手机号格式
    if (!validate_phone(phone)) {
        printf("错误：手机号格式不正确！\n");
        return 0;
    }
    
    // 创建新用户
    User* new_user = &system_data.users[system_data.user_count];
    new_user->id = system_data.next_user_id++;
    strcpy(new_user->username, username);
    strcpy(new_user->password, password);
    strcpy(new_user->email, email);
    strcpy(new_user->phone, phone);
    strcpy(new_user->real_name, real_name);
    new_user->role = ROLE_USER;
    new_user->status = STATUS_ACTIVE;
    new_user->created_at = time(NULL);
    new_user->updated_at = time(NULL);
    
    system_data.user_count++;
    
    printf("用户注册成功！用户ID: %d\n", new_user->id);
    return new_user->id;
}User* login_user(const char* username, const char* password) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (strcmp(system_data.users[i].username, username) == 0 && 
            strcmp(system_data.users[i].password, password) == 0) {
            if (system_data.users[i].status == STATUS_ACTIVE) {
                current_user = &system_data.users[i];
                printf("登录成功！欢迎 %s\n", current_user->real_name);
                return current_user;
            } else {
                printf("错误：账户已被禁用！\n");
                return NULL;
            }
        }
    }
    printf("错误：用户名或密码错误！\n");
    return NULL;
}

void logout_user(void) {
    if (current_user != NULL) {
        printf("用户 %s 已退出登录\n", current_user->real_name);
        current_user = NULL;
    }
}

int update_user_profile(int user_id, const char* email, const char* phone, const char* real_name) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (system_data.users[i].id == user_id) {
            if (!validate_email(email)) {
                printf("错误：邮箱格式不正确！\n");
                return 0;
            }
            
            if (!validate_phone(phone)) {
                printf("错误：手机号格式不正确！\n");
                return 0;
            }
            
            strcpy(system_data.users[i].email, email);
            strcpy(system_data.users[i].phone, phone);
            strcpy(system_data.users[i].real_name, real_name);
            system_data.users[i].updated_at = time(NULL);
            
            printf("用户信息更新成功！\n");
            return 1;
        }
    }
    printf("错误：用户不存在！\n");
    return 0;
}

int change_password(int user_id, const char* old_password, const char* new_password) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (system_data.users[i].id == user_id) {
            if (strcmp(system_data.users[i].password, old_password) != 0) {
                printf("错误：原密码不正确！\n");
                return 0;
            }
            
            strcpy(system_data.users[i].password, new_password);
            system_data.users[i].updated_at = time(NULL);
            
            printf("密码修改成功！\n");
            return 1;
        }
    }
    printf("错误：用户不存在！\n");
    return 0;
}