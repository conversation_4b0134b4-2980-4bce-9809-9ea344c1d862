#include "../include/badminton.h"

int register_user(const char* username, const char* password, const char* email, const char* phone, const char* real_name) {
    // 检查用户名是否已存在
    for (int i = 0; i < system_data.user_count; i++) {
        if (strcmp(system_data.users[i].username, username) == 0) {
            printf("错误：用户名已存在！\n");
            return 0;
        }
    }
    
    // 检查是否超过最大用户数
    if (system_data.user_count >= MAX_USERS) {
        printf("错误：用户数量已达上限！\n");
        return 0;
    }
    
    // 验证邮箱格式
    if (!validate_email(email)) {
        printf("错误：邮箱格式不正确！\n");
        return 0;
    }
    
    // 验证手机号格式
    if (!validate_phone(phone)) {
        printf("错误：手机号格式不正确！\n");
        return 0;
    }
    
    // 创建新用户
    User* new_user = &system_data.users[system_data.user_count];
    new_user->id = system_data.next_user_id++;
    strcpy(new_user->username, username);
    strcpy(new_user->password, password);
    strcpy(new_user->email, email);
    strcpy(new_user->phone, phone);
    strcpy(new_user->real_name, real_name);
    new_user->role = ROLE_USER;
    new_user->status = STATUS_ACTIVE;
    new_user->created_at = time(NULL);
    new_user->updated_at = time(NULL);
    
    system_data.user_count++;
    
    printf("用户注册成功！用户ID: %d\n", new_user->id);
    return new_user->id;
}User* login_user(const char* username, const char* password) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (strcmp(system_data.users[i].username, username) == 0 && 
            strcmp(system_data.users[i].password, password) == 0) {
            if (system_data.users[i].status == STATUS_ACTIVE) {
                current_user = &system_data.users[i];
                printf("登录成功！欢迎 %s\n", current_user->real_name);
                return current_user;
            } else {
                printf("错误：账户已被禁用！\n");
                return NULL;
            }
        }
    }
    printf("错误：用户名或密码错误！\n");
    return NULL;
}

void logout_user(void) {
    if (current_user != NULL) {
        printf("用户 %s 已退出登录\n", current_user->real_name);
        current_user = NULL;
    }
}

int update_user_profile(int user_id, const char* email, const char* phone, const char* real_name) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (system_data.users[i].id == user_id) {
            if (!validate_email(email)) {
                printf("错误：邮箱格式不正确！\n");
                return 0;
            }
            
            if (!validate_phone(phone)) {
                printf("错误：手机号格式不正确！\n");
                return 0;
            }
            
            strcpy(system_data.users[i].email, email);
            strcpy(system_data.users[i].phone, phone);
            strcpy(system_data.users[i].real_name, real_name);
            system_data.users[i].updated_at = time(NULL);
            
            printf("用户信息更新成功！\n");
            return 1;
        }
    }
    printf("错误：用户不存在！\n");
    return 0;
}

int change_password(int user_id, const char* old_password, const char* new_password) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (system_data.users[i].id == user_id) {
            if (strcmp(system_data.users[i].password, old_password) != 0) {
                printf("错误：原密码不正确！\n");
                return 0;
            }
            
            strcpy(system_data.users[i].password, new_password);
            system_data.users[i].updated_at = time(NULL);
            
            printf("密码修改成功！\n");
            return 1;
        }
    }
    printf("错误：用户不存在！\n");
    return 0;
}
void show_user_profile(int user_id) {
    User* user = get_user_by_id(user_id);
    if (user == NULL) {
        printf("错误：用户不存在！\n");
        return;
    }
    
    printf("\n==================== 个人资料 ====================\n");
    printf("用户ID：%d\n", user->id);
    printf("用户名：%s\n", user->username);
    printf("真实姓名：%s\n", user->real_name);
    printf("邮箱：%s\n", user->email);
    printf("手机号：%s\n", user->phone);
    printf("用户角色：%s\n", user->role == ROLE_ADMIN ? "管理员" : "普通用户");
    printf("账户状态：%s\n", user->is_active ? "正常" : "已停用");
    
    char created_time[64], updated_time[64];
    strftime(created_time, sizeof(created_time), "%Y-%m-%d %H:%M:%S", localtime(&user->created_at));
    strftime(updated_time, sizeof(updated_time), "%Y-%m-%d %H:%M:%S", localtime(&user->updated_at));
    
    printf("注册时间：%s\n", created_time);
    printf("更新时间：%s\n", updated_time);
    printf("================================================\n");
}

void show_user_center(int user_id) {
    User* user = get_user_by_id(user_id);
    if (user == NULL) {
        printf("错误：用户不存在！\n");
        return;
    }
    
    printf("\n==================== 用户中心 ====================\n");
    printf("欢迎，%s！\n", user->real_name);
    printf("================================================\n");
    
    // 显示用户基本信息
    printf("【个人信息】\n");
    printf("用户名：%s\n", user->username);
    printf("真实姓名：%s\n", user->real_name);
    printf("邮箱：%s\n", user->email);
    printf("手机号：%s\n", user->phone);
    printf("账户状态：%s\n", user->is_active ? "正常" : "已停用");
    printf("\n");
    
    // 显示用户统计信息
    show_user_statistics(user_id);
    
    printf("\n【功能菜单】\n");
    printf("1. 查看个人资料\n");
    printf("2. 修改个人信息\n");
    printf("3. 修改密码\n");
    printf("4. 查看预约历史\n");
    printf("5. 查看订单历史\n");
    printf("6. 账户设置\n");
    printf("0. 返回主菜单\n");
    printf("================================================\n");
}

void show_user_statistics(int user_id) {
    int total_reservations = 0;
    int confirmed_reservations = 0;
    int completed_reservations = 0;
    int cancelled_reservations = 0;
    double total_spent = 0.0;
    
    // 统计预约信息
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].user_id == user_id) {
            total_reservations++;
            switch (system_data.reservations[i].status) {
                case RESERVATION_CONFIRMED:
                    confirmed_reservations++;
                    break;
                case RESERVATION_COMPLETED:
                    completed_reservations++;
                    break;
                case RESERVATION_CANCELLED:
                    cancelled_reservations++;
                    break;
            }
        }
    }
    
    // 统计订单信息
    int total_orders = 0;
    int paid_orders = 0;
    for (int i = 0; i < system_data.order_count; i++) {
        // 通过预约ID找到对应的用户
        Reservation* reservation = get_reservation_by_id(system_data.orders[i].reservation_id);
        if (reservation && reservation->user_id == user_id) {
            total_orders++;
            if (system_data.orders[i].payment_status == PAYMENT_PAID) {
                paid_orders++;
                total_spent += system_data.orders[i].amount;
            }
        }
    }
    
    printf("【账户统计】\n");
    printf("总预约次数：%d 次\n", total_reservations);
    printf("已确认预约：%d 次\n", confirmed_reservations);
    printf("已完成预约：%d 次\n", completed_reservations);
    printf("已取消预约：%d 次\n", cancelled_reservations);
    printf("总订单数：%d 个\n", total_orders);
    printf("已支付订单：%d 个\n", paid_orders);
    printf("累计消费：%.2f 元\n", total_spent);
}

int deactivate_user_account(int user_id, const char* password) {
    User* user = get_user_by_id(user_id);
    if (user == NULL) {
        printf("错误：用户不存在！\n");
        return 0;
    }
    
    // 验证密码
    if (strcmp(user->password, password) != 0) {
        printf("错误：密码不正确！\n");
        return 0;
    }
    
    // 检查是否有未完成的预约
    int active_reservations = 0;
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].user_id == user_id && 
            (system_data.reservations[i].status == RESERVATION_PENDING || 
             system_data.reservations[i].status == RESERVATION_CONFIRMED)) {
            active_reservations++;
        }
    }
    
    if (active_reservations > 0) {
        printf("错误：您还有 %d 个未完成的预约，无法停用账户！\n", active_reservations);
        printf("请先取消或完成所有预约后再停用账户。\n");
        return 0;
    }
    
    // 检查是否有未支付的订单
    int pending_orders = 0;
    for (int i = 0; i < system_data.order_count; i++) {
        Reservation* reservation = get_reservation_by_id(system_data.orders[i].reservation_id);
        if (reservation && reservation->user_id == user_id && 
            system_data.orders[i].payment_status == PAYMENT_PENDING) {
            pending_orders++;
        }
    }
    
    if (pending_orders > 0) {
        printf("错误：您还有 %d 个未支付的订单，无法停用账户！\n", pending_orders);
        printf("请先处理所有未支付订单后再停用账户。\n");
        return 0;
    }
    
    user->is_active = 0;
    user->updated_at = time(NULL);
    
    printf("账户已成功停用！\n");
    printf("注意：停用后您将无法登录系统，如需重新激活请联系管理员。\n");
    
    return 1;
}

int reactivate_user_account(int user_id) {
    User* user = get_user_by_id(user_id);
    if (user == NULL) {
        printf("错误：用户不存在！\n");
        return 0;
    }
    
    if (user->is_active) {
        printf("错误：账户已经是激活状态！\n");
        return 0;
    }
    
    user->is_active = 1;
    user->updated_at = time(NULL);
    
    printf("账户已成功重新激活！\n");
    
    return 1;
}

User* get_user_by_id(int user_id) {
    for (int i = 0; i < system_data.user_count; i++) {
        if (system_data.users[i].id == user_id) {
            return &system_data.users[i];
        }
    }
    return NULL;
}

void list_all_users(void) {
    if (system_data.user_count == 0) {
        printf("暂无用户数据。\n");
        return;
    }
    
    printf("\n==================== 用户列表 ====================\n");
    printf("%-4s %-15s %-10s %-20s %-15s %-8s %-10s\n", 
           "ID", "用户名", "真实姓名", "邮箱", "手机号", "角色", "状态");
    printf("--------------------------------------------------------------------\n");
    
    for (int i = 0; i < system_data.user_count; i++) {
        User* user = &system_data.users[i];
        printf("%-4d %-15s %-10s %-20s %-15s %-8s %-10s\n",
               user->id,
               user->username,
               user->real_name,
               user->email,
               user->phone,
               user->role == ROLE_ADMIN ? "管理员" : "用户",
               user->is_active ? "正常" : "停用");
    }
    
    printf("--------------------------------------------------------------------\n");
    printf("总计：%d 个用户\n", system_data.user_count);
    printf("================================================\n");
}

void show_user_reservation_history(int user_id) {
    printf("\n==================== 预约历史 ====================\n");
    
    int user_reservations = 0;
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].user_id == user_id) {
            if (user_reservations == 0) {
                printf("%-4s %-10s %-15s %-25s %-10s %-8s %-12s\n",
                       "ID", "场地", "预约时间", "时间段", "金额", "状态", "创建时间");
                printf("--------------------------------------------------------------------------------\n");
            }
            
            Reservation* res = &system_data.reservations[i];
            Venue* venue = get_venue_by_id(res->venue_id);
            
            char start_time[20], end_time[20], created_time[20];
            strftime(start_time, sizeof(start_time), "%m-%d %H:%M", localtime(&res->start_time));
            strftime(end_time, sizeof(end_time), "%H:%M", localtime(&res->end_time));
            strftime(created_time, sizeof(created_time), "%m-%d %H:%M", localtime(&res->created_at));
            
            const char* status_str;
            switch (res->status) {
                case RESERVATION_PENDING: status_str = "待支付"; break;
                case RESERVATION_CONFIRMED: status_str = "已确认"; break;
                case RESERVATION_COMPLETED: status_str = "已完成"; break;
                case RESERVATION_CANCELLED: status_str = "已取消"; break;
                default: status_str = "未知"; break;
            }
            
            printf("%-4d %-10s %-15s %s-%s %-10.2f %-8s %-12s\n",
                   res->id,
                   venue ? venue->name : "未知场地",
                   start_time,
                   start_time, end_time,
                   res->total_amount,
                   status_str,
                   created_time);
            
            user_reservations++;
        }
    }
    
    if (user_reservations == 0) {
        printf("暂无预约记录。\n");
    } else {
        printf("--------------------------------------------------------------------------------\n");
        printf("总计：%d 条预约记录\n", user_reservations);
    }
    
    printf("================================================\n");
}

void show_user_order_history(int user_id) {
    printf("\n==================== 订单历史 ====================\n");
    
    int user_orders = 0;
    for (int i = 0; i < system_data.order_count; i++) {
        // 通过预约ID找到对应的用户
        Reservation* reservation = get_reservation_by_id(system_data.orders[i].reservation_id);
        if (reservation && reservation->user_id == user_id) {
            if (user_orders == 0) {
                printf("%-4s %-20s %-8s %-10s %-12s %-12s\n",
                       "ID", "订单号", "金额", "支付状态", "支付方式", "创建时间");
                printf("------------------------------------------------------------------------\n");
            }
            
            Order* order = &system_data.orders[i];
            
            char created_time[20];
            strftime(created_time, sizeof(created_time), "%m-%d %H:%M", localtime(&order->created_at));
            
            printf("%-4d %-20s %-8.2f %-10s %-12s %-12s\n",
                   order->id,
                   order->order_no,
                   order->amount,
                   get_payment_status_string(order->payment_status),
                   order->payment_method > 0 ? get_payment_method_string(order->payment_method) : "未选择",
                   created_time);
            
            user_orders++;
        }
    }
    
    if (user_orders == 0) {
        printf("暂无订单记录。\n");
    } else {
        printf("------------------------------------------------------------------------\n");
        printf("总计：%d 条订单记录\n", user_orders);
    }
    
    printf("================================================\n");
}

void user_center_menu(int user_id) {
    User* user = get_user_by_id(user_id);
    if (user == NULL) {
        printf("错误：用户不存在！\n");
        return;
    }
    
    int choice;
    char input[256];
    
    while (1) {
        show_user_center(user_id);
        printf("请选择操作：");
        
        if (fgets(input, sizeof(input), stdin) == NULL) {
            continue;
        }
        
        choice = atoi(input);
        
        switch (choice) {
            case 1:
                show_user_profile(user_id);
                printf("按回车键继续...");
                getchar();
                break;
                
            case 2: {
                printf("修改个人信息\n");
                printf("当前邮箱：%s\n", user->email);
                printf("请输入新邮箱（直接回车保持不变）：");
                char new_email[MAX_EMAIL_LEN];
                fgets(new_email, sizeof(new_email), stdin);
                if (new_email[0] == '\n') {
                    strcpy(new_email, user->email);
                } else {
                    new_email[strcspn(new_email, "\n")] = 0;
                }
                
                printf("当前手机号：%s\n", user->phone);
                printf("请输入新手机号（直接回车保持不变）：");
                char new_phone[MAX_PHONE_LEN];
                fgets(new_phone, sizeof(new_phone), stdin);
                if (new_phone[0] == '\n') {
                    strcpy(new_phone, user->phone);
                } else {
                    new_phone[strcspn(new_phone, "\n")] = 0;
                }
                
                printf("当前真实姓名：%s\n", user->real_name);
                printf("请输入新真实姓名（直接回车保持不变）：");
                char new_real_name[MAX_NAME_LEN];
                fgets(new_real_name, sizeof(new_real_name), stdin);
                if (new_real_name[0] == '\n') {
                    strcpy(new_real_name, user->real_name);
                } else {
                    new_real_name[strcspn(new_real_name, "\n")] = 0;
                }
                
                if (update_user_profile(user_id, new_email, new_phone, new_real_name)) {
                    printf("个人信息修改成功！\n");
                }
                printf("按回车键继续...");
                getchar();
                break;
            }
            
            case 3: {
                printf("修改密码\n");
                printf("请输入当前密码：");
                char old_password[MAX_PASSWORD_LEN];
                fgets(old_password, sizeof(old_password), stdin);
                old_password[strcspn(old_password, "\n")] = 0;
                
                printf("请输入新密码：");
                char new_password[MAX_PASSWORD_LEN];
                fgets(new_password, sizeof(new_password), stdin);
                new_password[strcspn(new_password, "\n")] = 0;
                
                printf("请再次输入新密码：");
                char confirm_password[MAX_PASSWORD_LEN];
                fgets(confirm_password, sizeof(confirm_password), stdin);
                confirm_password[strcspn(confirm_password, "\n")] = 0;
                
                if (strcmp(new_password, confirm_password) != 0) {
                    printf("错误：两次输入的密码不一致！\n");
                } else if (change_password(user_id, old_password, new_password)) {
                    printf("密码修改成功！\n");
                }
                printf("按回车键继续...");
                getchar();
                break;
            }
            
            case 4:
                show_user_reservation_history(user_id);
                printf("按回车键继续...");
                getchar();
                break;
                
            case 5:
                show_user_order_history(user_id);
                printf("按回车键继续...");
                getchar();
                break;
                
            case 6: {
                printf("账户设置\n");
                printf("1. 停用账户\n");
                printf("0. 返回\n");
                printf("请选择：");
                
                char setting_input[10];
                fgets(setting_input, sizeof(setting_input), stdin);
                int setting_choice = atoi(setting_input);
                
                if (setting_choice == 1) {
                    printf("警告：停用账户后您将无法登录系统！\n");
                    printf("确认停用账户吗？(y/N)：");
                    char confirm[10];
                    fgets(confirm, sizeof(confirm), stdin);
                    
                    if (confirm[0] == 'y' || confirm[0] == 'Y') {
                        printf("请输入密码确认：");
                        char password[MAX_PASSWORD_LEN];
                        fgets(password, sizeof(password), stdin);
                        password[strcspn(password, "\n")] = 0;
                        
                        if (deactivate_user_account(user_id, password)) {
                            printf("账户已停用，即将退出系统...\n");
                            printf("按回车键继续...");
                            getchar();
                            return;  // 退出用户中心
                        }
                    }
                }
                printf("按回车键继续...");
                getchar();
                break;
            }
            
            case 0:
                return;
                
            default:
                printf("无效选择，请重新输入！\n");
                printf("按回车键继续...");
                getchar();
                break;
        }
    }
}