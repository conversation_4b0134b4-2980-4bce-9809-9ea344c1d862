# 羽毛球馆预约系统 - 用户认证和权限管理系统文档

## 概述

本文档描述了羽毛球馆预约管理系统的用户认证和权限管理系统的完整实现，包括用户注册、登录验证、角色权限控制、密码管理等核心安全功能。

## 系统架构

### 1. 认证架构设计

```
用户请求 -> 身份验证 -> 权限检查 -> 功能访问
    |           |           |           |
    |           |           |           |
 用户输入 -> 密码验证 -> 角色验证 -> 菜单显示
```

### 2. 权限层级设计

```
超级管理员 (ROLE_SUPER_ADMIN = 4)
    |
    ├── 系统配置管理
    ├── 用户权限管理
    └── 所有管理员功能

管理员 (ROLE_ADMIN = 3)
    |
    ├── 场地管理
    ├── 预约管理
    ├── 订单管理
    └── 用户查看

教练 (ROLE_COACH = 2)
    |
    ├── 查看预约
    ├── 管理自己的课程
    └── 基础用户功能

普通用户 (ROLE_USER = 1)
    |
    ├── 场地预约
    ├── 个人信息管理
    └── 订单查看
```

## 核心功能实现

### 1. 用户注册功能

**函数签名：**
```c
int register_user(const char* username, const char* password, 
                 const char* email, const char* phone, const char* real_name);
```

**功能特性：**
- ✅ 用户名唯一性检查
- ✅ 邮箱格式验证
- ✅ 手机号格式验证（11位数字）
- ✅ 用户数量限制检查
- ✅ 自动分配用户ID
- ✅ 默认角色设置（普通用户）
- ✅ 时间戳记录

**验证规则：**
- 用户名：不能重复，长度限制50字符
- 密码：长度限制50字符
- 邮箱：必须包含@符号，格式验证
- 手机号：必须为11位数字
- 真实姓名：长度限制50字符

### 2. 用户登录功能

**函数签名：**
```c
User* login_user(const char* username, const char* password);
```

**功能特性：**
- ✅ 用户名密码验证
- ✅ 账户状态检查
- ✅ 会话管理（current_user）
- ✅ 登录成功提示
- ✅ 错误处理和提示

**安全机制：**
- 密码明文比较（生产环境建议加密）
- 账户状态验证（活跃/非活跃/已禁用）
- 登录失败提示
- 会话状态管理

### 3. 权限控制系统

**角色定义：**
```c
typedef enum {
    ROLE_USER = 1,        // 普通用户
    ROLE_COACH = 2,       // 教练
    ROLE_ADMIN = 3,       // 管理员
    ROLE_SUPER_ADMIN = 4  // 超级管理员
} UserRole;
```

**权限检查机制：**
- 基于角色的访问控制（RBAC）
- 菜单级权限控制
- 功能级权限验证
- 数据访问权限控制

### 4. 用户状态管理

**状态定义：**
```c
typedef enum {
    STATUS_ACTIVE = 1,    // 活跃状态
    STATUS_INACTIVE = 2,  // 非活跃状态
    STATUS_BANNED = 3     // 已禁用状态
} UserStatus;
```

**状态控制：**
- 新用户默认为活跃状态
- 管理员可以禁用用户账户
- 被禁用用户无法登录
- 状态变更记录时间戳

## 安全特性

### 1. 输入验证

**邮箱验证：**
```c
int validate_email(const char* email) {
    if (email == NULL || strlen(email) == 0) return 0;
    
    char* at_pos = strchr(email, '@');
    if (at_pos == NULL) return 0;
    
    if (at_pos == email || at_pos == email + strlen(email) - 1) return 0;
    
    return 1;
}
```

**手机号验证：**
```c
int validate_phone(const char* phone) {
    if (phone == NULL || strlen(phone) != 11) return 0;
    
    for (int i = 0; i < 11; i++) {
        if (!isdigit(phone[i])) return 0;
    }
    
    return 1;
}
```

### 2. 会话管理

**全局会话变量：**
```c
User* current_user = NULL;  // 当前登录用户
```

**会话控制：**
- 登录成功设置current_user
- 登出时清空current_user
- 权限检查基于current_user
- 会话超时处理（可扩展）

### 3. 数据保护

**用户数据结构：**
```c
typedef struct {
    int id;                           // 用户唯一标识
    char username[MAX_NAME_LEN];      // 用户名
    char password[MAX_PASSWORD_LEN];  // 密码（建议加密）
    char email[MAX_EMAIL_LEN];        // 邮箱
    char phone[MAX_PHONE_LEN];        // 手机号
    char real_name[MAX_NAME_LEN];     // 真实姓名
    UserRole role;                    // 用户角色
    UserStatus status;                // 用户状态
    time_t created_at;                // 创建时间
    time_t updated_at;                // 更新时间
} User;
```

## 权限控制实现

### 1. 菜单级权限控制

**管理员菜单访问：**
```c
if (current_user->role == ROLE_ADMIN || current_user->role == ROLE_SUPER_ADMIN) {
    show_admin_menu();
} else {
    show_user_menu();
}
```

### 2. 功能级权限控制

**场地管理权限：**
- 只有管理员和超级管理员可以添加/修改/删除场地
- 普通用户只能查看可用场地

**用户管理权限：**
- 超级管理员可以管理所有用户
- 管理员可以查看用户信息
- 普通用户只能管理自己的信息

**预约管理权限：**
- 管理员可以查看所有预约
- 用户只能查看和管理自己的预约
- 教练可以查看相关的预约信息

### 3. 数据访问权限

**用户数据访问：**
- 用户只能访问自己的个人信息
- 管理员可以访问所有用户信息
- 敏感信息（如密码）需要特殊处理

**预约数据访问：**
- 用户只能查看自己的预约记录
- 管理员可以查看所有预约记录
- 场地相关预约信息的访问控制## 安全增强建议

### 1. 密码安全

**当前实现：**
- 密码明文存储和比较
- 基础长度限制

**建议改进：**
- 使用哈希算法（如SHA-256）加密密码
- 添加盐值防止彩虹表攻击
- 实现密码强度检查
- 添加密码重试限制

**改进示例：**
```c
// 密码哈希函数（示例）
void hash_password(const char* password, char* hashed) {
    // 实现SHA-256哈希 + 盐值
    // 这里需要引入加密库
}

// 密码验证函数（示例）
int verify_password(const char* password, const char* hashed) {
    char temp_hash[65];
    hash_password(password, temp_hash);
    return strcmp(temp_hash, hashed) == 0;
}
```

### 2. 会话安全

**当前实现：**
- 简单的全局变量会话管理
- 基础的登录状态检查

**建议改进：**
- 实现会话超时机制
- 添加会话令牌（Token）
- 实现自动登出功能
- 添加并发登录控制

### 3. 输入安全

**当前实现：**
- 基础的邮箱和手机号验证
- 长度限制检查

**建议改进：**
- 添加SQL注入防护
- 实现输入过滤和转义
- 添加XSS防护
- 实现更严格的格式验证

## 测试用例

### 1. 用户注册测试

**测试场景：**
```c
// 正常注册
register_user("testuser", "password123", "<EMAIL>", "13800138001", "测试用户");

// 重复用户名
register_user("testuser", "password456", "<EMAIL>", "13800138002", "测试用户2");

// 无效邮箱
register_user("testuser2", "password123", "invalid-email", "13800138003", "测试用户3");

// 无效手机号
register_user("testuser3", "password123", "<EMAIL>", "1380013800", "测试用户4");
```

### 2. 用户登录测试

**测试场景：**
```c
// 正确登录
login_user("testuser", "password123");

// 错误密码
login_user("testuser", "wrongpassword");

// 不存在的用户
login_user("nonexistent", "password123");

// 被禁用的用户
// 首先禁用用户，然后尝试登录
```

### 3. 权限控制测试

**测试场景：**
- 普通用户尝试访问管理员功能
- 管理员访问所有功能
- 未登录用户尝试访问受保护功能
- 不同角色用户的菜单显示

## 性能优化

### 1. 用户查找优化

**当前实现：**
- 线性搜索用户数组
- O(n)时间复杂度

**优化建议：**
- 实现哈希表索引
- 按用户名排序并使用二分查找
- 添加用户缓存机制

### 2. 会话管理优化

**优化建议：**
- 实现会话池管理
- 添加会话清理机制
- 优化内存使用

## 扩展功能

### 1. 多因素认证

**实现建议：**
- 短信验证码
- 邮箱验证码
- TOTP（时间基础一次性密码）

### 2. 社交登录

**实现建议：**
- 微信登录集成
- QQ登录集成
- 支付宝登录集成

### 3. 审计日志

**实现建议：**
- 登录日志记录
- 权限变更日志
- 敏感操作审计

## 部署安全

### 1. 配置安全

**建议：**
- 使用环境变量存储敏感配置
- 实现配置文件加密
- 定期更新安全配置

### 2. 运行时安全

**建议：**
- 实现进程权限控制
- 添加内存保护机制
- 实现异常处理和恢复

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-06-26 | 初始版本，完成基础认证和权限管理 | Alex |

## 总结

本用户认证和权限管理系统实现了完整的用户注册、登录、权限控制等核心功能。通过基于角色的访问控制（RBAC）模型，确保了系统的安全性和可扩展性。虽然当前实现满足基本需求，但在生产环境中建议进一步加强密码安全、会话管理和输入验证等安全机制。

系统支持四种用户角色，提供了完整的权限分级管理，能够满足羽毛球馆预约系统的业务需求。通过合理的数据结构设计和权限控制机制，确保了用户数据的安全性和系统功能的正确访问控制。