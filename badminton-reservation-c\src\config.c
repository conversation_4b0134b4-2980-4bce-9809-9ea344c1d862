#include "../include/badminton.h"
#include "../include/config.h"
#include "../include/logger.h"

// 全局系统配置
SystemConfig system_config;

void init_system_config(void) {
    // 设置默认配置
    system_config.status = SYSTEM_NORMAL;
    system_config.business_hours_start = BUSINESS_HOURS_START;
    system_config.business_hours_end = BUSINESS_HOURS_END;
    system_config.advance_booking_days = ADVANCE_BOOKING_DAYS;
    system_config.cancellation_hours = CANCELLATION_HOURS;
    system_config.min_price_per_hour = MIN_PRICE_PER_HOUR;
    system_config.max_price_per_hour = MAX_PRICE_PER_HOUR;
    system_config.max_login_attempts = MAX_LOGIN_ATTEMPTS;
    system_config.session_timeout = SESSION_TIMEOUT;
    strcpy(system_config.system_notice, "系统正常运行中");
    system_config.last_updated = time(NULL);
    
    LOG_INFO_MSG("系统配置初始化完成");
}

void load_system_config(void) {
    FILE* file = fopen(CONFIG_DATA_FILE, "rb");
    if (file == NULL) {
        LOG_WARN_MSG("配置文件不存在，使用默认配置");
        init_system_config();
        save_system_config();
        return;
    }
    
    size_t read_count = fread(&system_config, sizeof(SystemConfig), 1, file);
    fclose(file);
    
    if (read_count != 1) {
        LOG_ERROR_MSG("配置文件读取失败，使用默认配置");
        init_system_config();
        save_system_config();
    } else {
        LOG_INFO_MSG("系统配置加载成功");
    }
}void save_system_config(void) {
    FILE* file = fopen(CONFIG_DATA_FILE, "wb");
    if (file == NULL) {
        LOG_ERROR_MSG("无法创建配置文件");
        return;
    }
    
    system_config.last_updated = time(NULL);
    size_t write_count = fwrite(&system_config, sizeof(SystemConfig), 1, file);
    fclose(file);
    
    if (write_count == 1) {
        LOG_INFO_MSG("系统配置保存成功");
    } else {
        LOG_ERROR_MSG("系统配置保存失败");
    }
}

int update_system_config(const SystemConfig* new_config) {
    if (new_config == NULL) {
        LOG_ERROR_MSG("配置参数为空");
        return 0;
    }
    
    // 验证配置参数
    if (new_config->business_hours_start < 0 || new_config->business_hours_start > 23 ||
        new_config->business_hours_end < 0 || new_config->business_hours_end > 23 ||
        new_config->business_hours_start >= new_config->business_hours_end) {
        LOG_ERROR_MSG("营业时间配置无效");
        return 0;
    }
    
    if (new_config->advance_booking_days < 1 || new_config->advance_booking_days > 30) {
        LOG_ERROR_MSG("提前预约天数配置无效");
        return 0;
    }
    
    if (new_config->cancellation_hours < 1 || new_config->cancellation_hours > 48) {
        LOG_ERROR_MSG("取消预约时间配置无效");
        return 0;
    }
    
    if (new_config->min_price_per_hour < 0 || new_config->max_price_per_hour < 0 ||
        new_config->min_price_per_hour > new_config->max_price_per_hour) {
        LOG_ERROR_MSG("价格配置无效");
        return 0;
    }
    
    // 更新配置
    system_config = *new_config;
    save_system_config();
    
    LOG_INFO_MSG("系统配置更新成功");
    return 1;
}

void reset_system_config(void) {
    LOG_INFO_MSG("重置系统配置为默认值");
    init_system_config();
    save_system_config();
}