# 羽毛球馆预约系统 - 场地管理功能文档

## 概述

本文档描述了羽毛球馆预约管理系统的场地管理功能的完整实现，包括场地信息的增删改查、场地状态管理、场地可用性检查、场地详情展示等核心功能。

## 系统架构

### 1. 场地管理架构设计

```
场地管理系统
    |
    ├── 场地信息管理
    │   ├── 添加场地
    │   ├── 修改场地
    │   ├── 删除场地
    │   └── 查询场地
    |
    ├── 场地状态管理
    │   ├── 可用状态
    │   ├── 维护状态
    │   └── 禁用状态
    |
    ├── 场地可用性检查
    │   ├── 实时可用性
    │   ├── 时间段检查
    │   └── 预约冲突检测
    |
    └── 场地展示功能
        ├── 场地列表
        ├── 场地详情
        └── 可用场地筛选
```

### 2. 数据模型设计

**场地数据结构：**
```c
typedef struct {
    int id;                           // 场地唯一标识
    char name[MAX_NAME_LEN];          // 场地名称
    char description[MAX_DESC_LEN];   // 场地描述
    int capacity;                     // 容纳人数
    double price_per_hour;            // 每小时价格
    char equipment[MAX_DESC_LEN];     // 设备配置
    VenueStatus status;               // 场地状态
    time_t created_at;                // 创建时间
    time_t updated_at;                // 更新时间
} Venue;
```

**场地状态枚举：**
```c
typedef enum {
    VENUE_AVAILABLE = 1,    // 可用状态
    VENUE_MAINTENANCE = 2,  // 维护中状态
    VENUE_DISABLED = 3      // 已禁用状态
} VenueStatus;
```

## 核心功能实现

### 1. 场地添加功能

**函数签名：**
```c
int add_venue(const char* name, const char* description, int capacity, 
              double price_per_hour, const char* equipment);
```

**功能特性：**
- ✅ 场地数量限制检查（MAX_VENUES = 50）
- ✅ 自动分配场地ID
- ✅ 默认状态设置（VENUE_AVAILABLE）
- ✅ 时间戳记录（创建时间、更新时间）
- ✅ 输入参数验证
- ✅ 成功反馈和错误处理

**验证规则：**
- 场地名称：长度限制50字符，不能为空
- 场地描述：长度限制200字符
- 容纳人数：必须大于0
- 价格：必须大于0
- 设备配置：长度限制200字符

**实现示例：**
```c
int add_venue(const char* name, const char* description, int capacity, 
              double price_per_hour, const char* equipment) {
    if (system_data.venue_count >= MAX_VENUES) {
        printf("错误：场地数量已达上限！\n");
        return 0;
    }
    
    Venue* new_venue = &system_data.venues[system_data.venue_count];
    new_venue->id = system_data.next_venue_id++;
    strcpy(new_venue->name, name);
    strcpy(new_venue->description, description);
    new_venue->capacity = capacity;
    new_venue->price_per_hour = price_per_hour;
    strcpy(new_venue->equipment, equipment);
    new_venue->status = VENUE_AVAILABLE;
    new_venue->created_at = time(NULL);
    new_venue->updated_at = time(NULL);
    
    system_data.venue_count++;
    
    printf("场地添加成功！场地ID: %d\n", new_venue->id);
    return new_venue->id;
}
```

### 2. 场地修改功能

**函数签名：**
```c
int update_venue(int venue_id, const char* name, const char* description, 
                 int capacity, double price_per_hour, const char* equipment);
```

**功能特性：**
- ✅ 场地存在性检查
- ✅ 全字段更新支持
- ✅ 更新时间自动记录
- ✅ 输入参数验证
- ✅ 操作结果反馈

**实现逻辑：**
1. 根据venue_id查找场地
2. 验证场地是否存在
3. 更新所有可修改字段
4. 更新时间戳
5. 返回操作结果

### 3. 场地删除功能

**函数签名：**
```c
int delete_venue(int venue_id);
```

**功能特性：**
- ✅ 场地存在性检查
- ✅ 预约依赖性检查（防止删除有预约的场地）
- ✅ 数组元素安全移动
- ✅ 计数器更新
- ✅ 完整性保护

**安全机制：**
```c
// 检查是否有未完成的预约
for (int j = 0; j < system_data.reservation_count; j++) {
    if (system_data.reservations[j].venue_id == venue_id && 
        (system_data.reservations[j].status == RESERVATION_PENDING || 
         system_data.reservations[j].status == RESERVATION_CONFIRMED)) {
        printf("错误：该场地还有未完成的预约，无法删除！\n");
        return 0;
    }
}
```

### 4. 场地查询功能

**按ID查询：**
```c
Venue* get_venue_by_id(int venue_id);
```

**场地列表显示：**
```c
void list_venues(void);
```

**可用场地筛选：**
```c
void list_available_venues(void);
```

**功能特性：**
- ✅ 快速ID查找
- ✅ 格式化列表显示
- ✅ 状态筛选功能
- ✅ 详细信息展示
- ✅ 用户友好的界面

## 场地状态管理

### 1. 状态定义

| 状态值 | 状态名称 | 描述 | 可预约 |
|--------|----------|------|--------|
| 1 | VENUE_AVAILABLE | 可用状态 | ✅ |
| 2 | VENUE_MAINTENANCE | 维护中 | ❌ |
| 3 | VENUE_DISABLED | 已禁用 | ❌ |

### 2. 状态转换规则

```
VENUE_AVAILABLE ←→ VENUE_MAINTENANCE
        ↓
VENUE_DISABLED
```

**状态转换逻辑：**
- 新建场地默认为可用状态
- 可用状态可以转为维护或禁用
- 维护状态可以恢复为可用
- 禁用状态需要管理员权限恢复

### 3. 状态显示

**状态字符串映射：**
```c
const char* status_str;
switch (venue->status) {
    case VENUE_AVAILABLE: status_str = "可用"; break;
    case VENUE_MAINTENANCE: status_str = "维护中"; break;
    case VENUE_DISABLED: status_str = "已禁用"; break;
    default: status_str = "未知"; break;
}
```

## 场地可用性检查

### 1. 基础可用性检查

**检查条件：**
- 场地状态必须为VENUE_AVAILABLE
- 场地必须存在于系统中
- 场地信息必须完整

### 2. 时间段可用性检查

**实现逻辑：**
1. 检查场地基础可用性
2. 查询指定时间段的预约记录
3. 检测时间冲突
4. 返回可用性结果

### 3. 实时可用性查询

**功能特性：**
- 实时查询当前可用场地
- 按时间段筛选可用场地
- 支持批量可用性检查
- 提供详细的不可用原因

## 用户界面设计

### 1. 场地列表界面

**显示内容：**
```
==================== 场地列表 ====================
ID    名称          容量    价格/小时    状态
================================================
1     1号场地       4       60.00       可用
2     2号场地       4       80.00       维护中
3     3号场地       6       50.00       可用
================================================
```

### 2. 场地详情界面

**显示内容：**
- 场地基本信息（名称、描述、容量）
- 价格信息（每小时价格）
- 设备配置详情
- 当前状态
- 创建和更新时间

### 3. 管理员场地管理界面

**功能菜单：**
- 添加新场地
- 修改场地信息
- 删除场地
- 查看场地列表
- 场地状态管理

## 权限控制

### 1. 管理员权限

**可执行操作：**
- ✅ 添加场地
- ✅ 修改场地信息
- ✅ 删除场地
- ✅ 修改场地状态
- ✅ 查看所有场地

### 2. 普通用户权限

**可执行操作：**
- ✅ 查看可用场地列表
- ✅ 查看场地详情
- ❌ 修改场地信息
- ❌ 删除场地
- ❌ 修改场地状态

### 3. 权限检查机制

**实现方式：**
- 菜单级权限控制
- 功能调用前权限验证
- 基于用户角色的访问控制## 数据完整性保护

### 1. 引用完整性

**预约关联检查：**
- 删除场地前检查是否有未完成的预约
- 禁用场地前检查是否有未来的预约
- 修改场地信息时保持预约关联的有效性

**实现机制：**
```c
// 检查场地是否有未完成的预约
int has_pending_reservations(int venue_id) {
    for (int i = 0; i < system_data.reservation_count; i++) {
        if (system_data.reservations[i].venue_id == venue_id && 
            (system_data.reservations[i].status == RESERVATION_PENDING || 
             system_data.reservations[i].status == RESERVATION_CONFIRMED)) {
            return 1;
        }
    }
    return 0;
}
```

### 2. 数据一致性

**一致性保证：**
- 场地ID的唯一性
- 状态转换的合法性
- 价格信息的有效性
- 时间戳的准确性

### 3. 事务性操作

**原子性保证：**
- 场地信息的完整更新
- 状态变更的一致性
- 错误回滚机制

## 性能优化

### 1. 查询优化

**当前实现：**
- 线性搜索场地数组
- O(n)时间复杂度

**优化建议：**
- 实现哈希表索引
- 按场地ID排序并使用二分查找
- 添加场地缓存机制

**优化示例：**
```c
// 场地索引结构
typedef struct {
    int venue_id;
    int array_index;
} VenueIndex;

// 二分查找优化
Venue* get_venue_by_id_optimized(int venue_id) {
    // 实现二分查找逻辑
    int left = 0, right = system_data.venue_count - 1;
    while (left <= right) {
        int mid = (left + right) / 2;
        if (system_data.venues[mid].id == venue_id) {
            return &system_data.venues[mid];
        } else if (system_data.venues[mid].id < venue_id) {
            left = mid + 1;
        } else {
            right = mid - 1;
        }
    }
    return NULL;
}
```

### 2. 内存优化

**优化策略：**
- 合理设置MAX_VENUES常量
- 优化字符串存储
- 减少内存碎片

### 3. 缓存策略

**缓存机制：**
- 可用场地列表缓存
- 场地状态缓存
- 查询结果缓存

## 扩展功能

### 1. 场地图片管理

**功能设计：**
```c
typedef struct {
    int venue_id;
    char image_path[256];
    char image_description[100];
    time_t uploaded_at;
} VenueImage;
```

**实现建议：**
- 图片文件存储管理
- 图片格式验证
- 图片大小限制
- 缩略图生成

### 2. 场地设备管理

**功能设计：**
```c
typedef struct {
    int equipment_id;
    char name[50];
    int quantity;
    char condition[20];  // 良好/一般/需维修
} Equipment;

typedef struct {
    int venue_id;
    int equipment_id;
    int available_quantity;
} VenueEquipment;
```

### 3. 场地时间段管理

**功能设计：**
```c
typedef struct {
    int venue_id;
    int day_of_week;     // 0-6 (周日到周六)
    time_t start_time;   // 开始时间
    time_t end_time;     // 结束时间
    double price_rate;   // 价格倍率
} VenueSchedule;
```

### 4. 场地评价系统

**功能设计：**
```c
typedef struct {
    int venue_id;
    int user_id;
    int rating;          // 1-5星评分
    char comment[200];   // 评价内容
    time_t created_at;
} VenueReview;
```

## 测试用例

### 1. 场地添加测试

**测试场景：**
```c
// 正常添加
add_venue("测试场地", "测试描述", 4, 60.0, "标准设备");

// 超出数量限制
// 添加50个场地后再添加第51个

// 无效参数
add_venue("", "描述", 4, 60.0, "设备");  // 空名称
add_venue("场地", "描述", 0, 60.0, "设备");  // 无效容量
add_venue("场地", "描述", 4, -10.0, "设备");  // 负价格
```

### 2. 场地修改测试

**测试场景：**
```c
// 正常修改
update_venue(1, "新名称", "新描述", 6, 80.0, "新设备");

// 修改不存在的场地
update_venue(999, "名称", "描述", 4, 60.0, "设备");

// 无效参数修改
update_venue(1, "", "描述", 4, 60.0, "设备");
```

### 3. 场地删除测试

**测试场景：**
```c
// 删除无预约的场地
delete_venue(1);

// 删除有预约的场地
// 先创建预约，再尝试删除

// 删除不存在的场地
delete_venue(999);
```

### 4. 场地查询测试

**测试场景：**
```c
// 查询存在的场地
get_venue_by_id(1);

// 查询不存在的场地
get_venue_by_id(999);

// 列表显示测试
list_venues();
list_available_venues();
```

## 错误处理

### 1. 输入验证错误

**错误类型：**
- 空字符串输入
- 超长字符串输入
- 无效数值输入
- 格式错误输入

**处理策略：**
- 参数验证
- 错误提示
- 安全返回

### 2. 业务逻辑错误

**错误类型：**
- 场地不存在
- 场地数量超限
- 依赖关系冲突
- 状态转换错误

**处理策略：**
- 业务规则检查
- 友好错误提示
- 操作回滚

### 3. 系统错误

**错误类型：**
- 内存不足
- 文件操作失败
- 数据损坏

**处理策略：**
- 异常捕获
- 错误日志
- 系统恢复

## 部署配置

### 1. 系统配置

**配置参数：**
```c
#define MAX_VENUES 50              // 最大场地数量
#define MAX_NAME_LEN 50            // 名称最大长度
#define MAX_DESC_LEN 200           // 描述最大长度
#define DEFAULT_VENUE_PRICE 50.0   // 默认场地价格
```

### 2. 数据初始化

**默认场地数据：**
```c
void init_default_venues() {
    add_venue("1号场地", "标准羽毛球场地，光线充足，设备齐全", 4, 60.0, "羽毛球拍2副，羽毛球6个，计分牌");
    add_venue("2号场地", "高级羽毛球场地，专业地胶，适合比赛", 4, 80.0, "专业羽毛球拍4副，比赛用球12个，电子计分器");
    add_venue("3号场地", "训练专用场地，适合教学和训练", 6, 50.0, "训练用拍6副，练习球20个，训练器材");
}
```

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-06-26 | 初始版本，完成基础场地管理功能 | Alex |

## 总结

本场地管理系统实现了完整的场地信息管理功能，包括增删改查、状态管理、可用性检查等核心功能。通过合理的数据结构设计和完善的业务逻辑，确保了场地数据的完整性和系统的稳定性。

系统支持多种场地状态管理，提供了完整的权限控制机制，能够满足羽毛球馆的日常运营需求。通过优化的查询算法和缓存策略，保证了系统的性能和响应速度。

扩展功能设计为系统的未来发展提供了良好的基础，包括图片管理、设备管理、时间段管理和评价系统等高级功能。完整的测试用例和错误处理机制确保了系统的可靠性和用户体验。