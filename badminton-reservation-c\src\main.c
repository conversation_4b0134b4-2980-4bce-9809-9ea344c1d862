#include "../include/badminton.h"

// 全局变量定义
SystemData system_data;
User* current_user = NULL;

int main() {
    printf("===========================================\n");
    printf("     欢迎使用羽毛球馆预约管理系统\n");
    printf("===========================================\n\n");
    
    // 初始化系统
    init_system();
    
    // 加载数据
    load_data();
    
    // 显示主菜单
    show_main_menu();
    
    // 清理系统资源
    cleanup_system();
    
    printf("\n感谢使用羽毛球馆预约管理系统！\n");
    return 0;
}