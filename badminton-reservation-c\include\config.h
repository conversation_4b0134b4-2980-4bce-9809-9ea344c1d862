#ifndef CONFIG_H
#define CONFIG_H

// 系统配置常量
#define SYSTEM_VERSION "1.0.0"
#define SYSTEM_NAME "羽毛球馆预约管理系统"
#define SYSTEM_AUTHOR "米醋电子工作室"

// 业务配置
#define BUSINESS_HOURS_START 8    // 营业开始时间（小时）
#define BUSINESS_HOURS_END 22     // 营业结束时间（小时）
#define ADVANCE_BOOKING_DAYS 7    // 提前预约天数
#define CANCELLATION_HOURS 2      // 取消预约提前小时数
#define MIN_BOOKING_DURATION 1    // 最小预约时长（小时）
#define MAX_BOOKING_DURATION 8    // 最大预约时长（小时）

// 价格配置
#define MIN_PRICE_PER_HOUR 50.0   // 最低每小时价格
#define MAX_PRICE_PER_HOUR 500.0  // 最高每小时价格

// 数据文件路径
#define DATA_DIR "data"
#define USER_DATA_FILE "data/users.dat"
#define VENUE_DATA_FILE "data/venues.dat"
#define RESERVATION_DATA_FILE "data/reservations.dat"
#define ORDER_DATA_FILE "data/orders.dat"
#define CONFIG_DATA_FILE "data/config.dat"

// 日志配置
#define LOG_FILE "data/system.log"
#define MAX_LOG_SIZE 1048576      // 1MB

// 安全配置
#define MAX_LOGIN_ATTEMPTS 3      // 最大登录尝试次数
#define SESSION_TIMEOUT 3600      // 会话超时时间（秒）
#define PASSWORD_MIN_LENGTH 6     // 密码最小长度
#define PASSWORD_MAX_LENGTH 20    // 密码最大长度

// 显示配置
#define ITEMS_PER_PAGE 10         // 每页显示项目数
#define MAX_MENU_CHOICE 20        // 最大菜单选项数

// 系统状态
typedef enum {
    SYSTEM_NORMAL = 0,
    SYSTEM_MAINTENANCE = 1,
    SYSTEM_EMERGENCY = 2
} SystemStatus;

// 系统配置结构体
typedef struct {
    SystemStatus status;
    int business_hours_start;
    int business_hours_end;
    int advance_booking_days;
    int cancellation_hours;
    double min_price_per_hour;
    double max_price_per_hour;
    int max_login_attempts;
    int session_timeout;
    char system_notice[200];
    time_t last_updated;
} SystemConfig;

// 全局系统配置
extern SystemConfig system_config;

// 配置管理函数
void init_system_config(void);
void load_system_config(void);
void save_system_config(void);
int update_system_config(const SystemConfig* new_config);
void reset_system_config(void);

#endif // CONFIG_H