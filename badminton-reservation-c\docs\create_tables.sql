-- 羽毛球馆预约系统数据库建表脚本
-- 数据库：badminton_reservation
-- 版本：1.0
-- 创建日期：2025-06-26

-- 创建数据库
CREATE DATABASE IF NOT EXISTS badminton_reservation 
CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE badminton_reservation;

-- 1. 用户表
CREATE TABLE users (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '用户唯一标识',
    username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
    password VARCHAR(50) NOT NULL COMMENT '密码',
    email VARCHAR(100) NOT NULL UNIQUE COMMENT '邮箱地址',
    phone VARCHAR(20) NOT NULL UNIQUE COMMENT '手机号码',
    real_name VARCHAR(50) NOT NULL COMMENT '真实姓名',
    role ENUM('1', '2', '3', '4') NOT NULL DEFAULT '1' COMMENT '用户角色：1-普通用户, 2-教练, 3-管理员, 4-超级管理员',
    status ENUM('1', '2', '3') NOT NULL DEFAULT '1' COMMENT '用户状态：1-活跃, 2-非活跃, 3-已禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_role (role),
    INDEX idx_status (status)
) ENGINE=InnoDB COMMENT='用户表';

-- 2. 场地表
CREATE TABLE venues (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '场地唯一标识',
    name VARCHAR(50) NOT NULL COMMENT '场地名称',
    description TEXT COMMENT '场地描述',
    capacity INT NOT NULL COMMENT '容纳人数',
    price_per_hour DECIMAL(10,2) NOT NULL COMMENT '每小时价格',
    equipment TEXT COMMENT '设备配置',
    status ENUM('1', '2', '3') NOT NULL DEFAULT '1' COMMENT '场地状态：1-可用, 2-维护中, 3-已禁用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    INDEX idx_status (status),
    INDEX idx_price (price_per_hour)
) ENGINE=InnoDB COMMENT='场地表';

-- 3. 预约表
CREATE TABLE reservations (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '预约唯一标识',
    user_id INT NOT NULL COMMENT '用户ID',
    venue_id INT NOT NULL COMMENT '场地ID',
    start_time TIMESTAMP NOT NULL COMMENT '开始时间',
    end_time TIMESTAMP NOT NULL COMMENT '结束时间',
    total_amount DECIMAL(10,2) NOT NULL COMMENT '总金额',
    status ENUM('1', '2', '3', '4') NOT NULL DEFAULT '1' COMMENT '预约状态：1-待支付, 2-已确认, 3-已完成, 4-已取消',
    notes TEXT COMMENT '备注信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (venue_id) REFERENCES venues(id) ON DELETE CASCADE,
    INDEX idx_user_id (user_id),
    INDEX idx_venue_id (venue_id),
    INDEX idx_start_time (start_time),
    INDEX idx_end_time (end_time),
    INDEX idx_status (status),
    INDEX idx_venue_time (venue_id, start_time, end_time) COMMENT '用于时间冲突检测'
) ENGINE=InnoDB COMMENT='预约表';-- 4. 订单表
CREATE TABLE orders (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '订单唯一标识',
    reservation_id INT NOT NULL COMMENT '预约ID',
    order_no VARCHAR(32) NOT NULL UNIQUE COMMENT '订单号',
    amount DECIMAL(10,2) NOT NULL COMMENT '订单金额',
    payment_status ENUM('1', '2', '3', '4') NOT NULL DEFAULT '1' COMMENT '支付状态：1-待支付, 2-已支付, 3-已退款, 4-支付失败',
    paid_at TIMESTAMP NULL COMMENT '支付时间',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    
    FOREIGN KEY (reservation_id) REFERENCES reservations(id) ON DELETE CASCADE,
    INDEX idx_reservation_id (reservation_id),
    INDEX idx_payment_status (payment_status),
    INDEX idx_created_at (created_at)
) ENGINE=InnoDB COMMENT='订单表';

-- 5. 系统配置表
CREATE TABLE system_config (
    id INT AUTO_INCREMENT PRIMARY KEY COMMENT '配置ID',
    config_key VARCHAR(50) NOT NULL UNIQUE COMMENT '配置键',
    config_value TEXT NOT NULL COMMENT '配置值',
    description VARCHAR(200) COMMENT '配置描述',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间'
) ENGINE=InnoDB COMMENT='系统配置表';

-- 插入默认系统配置
INSERT INTO system_config (config_key, config_value, description) VALUES
('business_start_hour', '6', '营业开始时间（小时）'),
('business_end_hour', '23', '营业结束时间（小时）'),
('min_reservation_duration', '60', '最小预约时长（分钟）'),
('max_reservation_duration', '180', '最大预约时长（分钟）'),
('advance_booking_days', '30', '提前预约天数'),
('cancellation_hours', '2', '取消预约提前小时数'),
('default_venue_price', '50.00', '默认场地价格'),
('system_name', '羽毛球馆预约管理系统', '系统名称'),
('admin_email', '<EMAIL>', '管理员邮箱'),
('max_concurrent_reservations', '3', '用户最大并发预约数');

-- 插入默认管理员用户
INSERT INTO users (username, password, email, phone, real_name, role, status) VALUES
('admin', 'admin123', '<EMAIL>', '***********', '系统管理员', '4', '1');

-- 插入示例场地数据
INSERT INTO venues (name, description, capacity, price_per_hour, equipment, status) VALUES
('1号场地', '标准羽毛球场地，光线充足，设备齐全', 4, 60.00, '羽毛球拍2副，羽毛球6个，计分牌', '1'),
('2号场地', '高级羽毛球场地，专业地胶，适合比赛', 4, 80.00, '专业羽毛球拍4副，比赛用球12个，电子计分器', '1'),
('3号场地', '训练专用场地，适合教学和训练', 6, 50.00, '训练用拍6副，练习球20个，训练器材', '1');

-- 创建视图：用户预约统计
CREATE VIEW user_reservation_stats AS
SELECT 
    u.id,
    u.username,
    u.real_name,
    COUNT(r.id) as total_reservations,
    SUM(CASE WHEN r.status = '2' THEN 1 ELSE 0 END) as confirmed_reservations,
    SUM(CASE WHEN r.status = '3' THEN 1 ELSE 0 END) as completed_reservations,
    SUM(CASE WHEN r.status = '4' THEN 1 ELSE 0 END) as cancelled_reservations,
    SUM(r.total_amount) as total_amount
FROM users u
LEFT JOIN reservations r ON u.id = r.user_id
GROUP BY u.id, u.username, u.real_name;

-- 创建视图：场地使用统计
CREATE VIEW venue_usage_stats AS
SELECT 
    v.id,
    v.name,
    v.price_per_hour,
    COUNT(r.id) as total_reservations,
    SUM(CASE WHEN r.status IN ('2', '3') THEN 1 ELSE 0 END) as successful_reservations,
    SUM(r.total_amount) as total_revenue,
    AVG(r.total_amount) as avg_revenue_per_reservation
FROM venues v
LEFT JOIN reservations r ON v.id = r.venue_id
GROUP BY v.id, v.name, v.price_per_hour;