package com.badminton;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 羽毛球馆预约系统主启动类
 */
@SpringBootApplication
@MapperScan("com.badminton.mapper")
@EnableScheduling
public class BadmintonReservationApplication {

    public static void main(String[] args) {
        SpringApplication.run(BadmintonReservationApplication.class, args);
        System.out.println("羽毛球馆预约系统启动成功！");
        System.out.println("访问地址：http://localhost:8080/api");
    }
}