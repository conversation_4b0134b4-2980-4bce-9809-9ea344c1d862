#include "../include/badminton.h"

void load_data(void) {
    FILE *file;
    
    // 加载用户数据
    file = fopen("data/users.dat", "rb");
    if (file != NULL) {
        fread(&system_data.user_count, sizeof(int), 1, file);
        fread(&system_data.next_user_id, sizeof(int), 1, file);
        fread(system_data.users, sizeof(User), system_data.user_count, file);
        fclose(file);
        printf("用户数据加载完成，共 %d 个用户\n", system_data.user_count);
    }
    
    // 加载场地数据
    file = fopen("data/venues.dat", "rb");
    if (file != NULL) {
        fread(&system_data.venue_count, sizeof(int), 1, file);
        fread(&system_data.next_venue_id, sizeof(int), 1, file);
        fread(system_data.venues, sizeof(Venue), system_data.venue_count, file);
        fclose(file);
        printf("场地数据加载完成，共 %d 个场地\n", system_data.venue_count);
    }
    
    // 加载预约数据
    file = fopen("data/reservations.dat", "rb");
    if (file != NULL) {
        fread(&system_data.reservation_count, sizeof(int), 1, file);
        fread(&system_data.next_reservation_id, sizeof(int), 1, file);
        fread(system_data.reservations, sizeof(Reservation), system_data.reservation_count, file);
        fclose(file);
        printf("预约数据加载完成，共 %d 个预约\n", system_data.reservation_count);
    }
    
    // 加载订单数据
    file = fopen("data/orders.dat", "rb");
    if (file != NULL) {
        fread(&system_data.order_count, sizeof(int), 1, file);
        fread(&system_data.next_order_id, sizeof(int), 1, file);
        fread(system_data.orders, sizeof(Order), system_data.order_count, file);
        fclose(file);
        printf("订单数据加载完成，共 %d 个订单\n", system_data.order_count);
    }
}
void save_data(void) {
    FILE *file;
    
    // 保存用户数据
    file = fopen("data/users.dat", "wb");
    if (file != NULL) {
        fwrite(&system_data.user_count, sizeof(int), 1, file);
        fwrite(&system_data.next_user_id, sizeof(int), 1, file);
        fwrite(system_data.users, sizeof(User), system_data.user_count, file);
        fclose(file);
    }
    
    // 保存场地数据
    file = fopen("data/venues.dat", "wb");
    if (file != NULL) {
        fwrite(&system_data.venue_count, sizeof(int), 1, file);
        fwrite(&system_data.next_venue_id, sizeof(int), 1, file);
        fwrite(system_data.venues, sizeof(Venue), system_data.venue_count, file);
        fclose(file);
    }
    
    // 保存预约数据
    file = fopen("data/reservations.dat", "wb");
    if (file != NULL) {
        fwrite(&system_data.reservation_count, sizeof(int), 1, file);
        fwrite(&system_data.next_reservation_id, sizeof(int), 1, file);
        fwrite(system_data.reservations, sizeof(Reservation), system_data.reservation_count, file);
        fclose(file);
    }
    
    // 保存订单数据
    file = fopen("data/orders.dat", "wb");
    if (file != NULL) {
        fwrite(&system_data.order_count, sizeof(int), 1, file);
        fwrite(&system_data.next_order_id, sizeof(int), 1, file);
        fwrite(system_data.orders, sizeof(Order), system_data.order_count, file);
        fclose(file);
    }
    
    printf("数据保存完成！\n");
}

