#ifndef LOGGER_H
#define LOGGER_H

#include <stdio.h>
#include <time.h>

// 日志级别
typedef enum {
    LOG_DEBUG = 0,
    LOG_INFO = 1,
    LOG_WARN = 2,
    LOG_ERROR = 3,
    LOG_FATAL = 4
} LogLevel;

// 日志函数声明
void init_logger(void);
void log_message(LogLevel level, const char* format, ...);
void log_user_action(int user_id, const char* action);
void log_system_event(const char* event);
void log_error(const char* error_msg);
void cleanup_logger(void);

// 便捷宏定义
#define LOG_DEBUG_MSG(msg, ...) log_message(LOG_DEBUG, msg, ##__VA_ARGS__)
#define LOG_INFO_MSG(msg, ...) log_message(LOG_INFO, msg, ##__VA_ARGS__)
#define LOG_WARN_MSG(msg, ...) log_message(LOG_WARN, msg, ##__VA_ARGS__)
#define LOG_ERROR_MSG(msg, ...) log_message(LOG_ERROR, msg, ##__VA_ARGS__)
#define LOG_FATAL_MSG(msg, ...) log_message(LOG_FATAL, msg, ##__VA_ARGS__)

#endif // LOGGER_H