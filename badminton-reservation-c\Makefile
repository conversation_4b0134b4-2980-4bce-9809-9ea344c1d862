# 羽毛球馆预约系统 Makefile

CC = gcc
CFLAGS = -Wall -Wextra -std=c99 -Iinclude
TARGET = badminton_system
SRCDIR = src
OBJDIR = build
DATADIR = data

# 源文件
SOURCES = $(wildcard $(SRCDIR)/*.c)
OBJECTS = $(SOURCES:$(SRCDIR)/%.c=$(OBJDIR)/%.o)

# 默认目标
all: $(TARGET)

# 创建目标文件目录
$(OBJDIR):
	mkdir -p $(OBJDIR)

# 创建数据目录
$(DATADIR):
	mkdir -p $(DATADIR)

# 编译目标文件
$(OBJDIR)/%.o: $(SRCDIR)/%.c | $(OBJDIR)
	$(CC) $(CFLAGS) -c $< -o $@

# 链接生成可执行文件
$(TARGET): $(OBJECTS) | $(DATADIR)
	$(CC) $(OBJECTS) -o $@

# 清理编译文件
clean:
	rm -rf $(OBJDIR) $(TARGET)

# 清理所有文件（包括数据）
clean-all: clean
	rm -rf $(DATADIR)

# 运行程序
run: $(TARGET)
	./$(TARGET)

# 调试版本
debug: CFLAGS += -g -DDEBUG
debug: $(TARGET)

# 发布版本
release: CFLAGS += -O2 -DNDEBUG
release: $(TARGET)

# 安装（复制到系统目录）
install: $(TARGET)
	cp $(TARGET) /usr/local/bin/

# 卸载
uninstall:
	rm -f /usr/local/bin/$(TARGET)

.PHONY: all clean clean-all run debug release install uninstall