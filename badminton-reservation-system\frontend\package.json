{"name": "badminton-reservation-frontend", "version": "1.0.0", "description": "羽毛球馆预约系统前端", "scripts": {"serve": "vue-cli-service serve", "build": "vue-cli-service build", "lint": "vue-cli-service lint"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.1.0", "element-plus": "^2.3.8", "axios": "^1.4.0", "@element-plus/icons-vue": "^2.1.0", "dayjs": "^1.11.9", "echarts": "^5.4.3", "vue-echarts": "^6.6.1"}, "devDependencies": {"@vue/cli-service": "^5.0.8", "@vue/compiler-sfc": "^3.3.4", "sass": "^1.64.1", "sass-loader": "^13.3.2"}, "browserslist": ["> 1%", "last 2 versions", "not dead", "not ie 11"]}