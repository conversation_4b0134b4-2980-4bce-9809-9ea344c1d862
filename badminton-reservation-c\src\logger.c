#include "../include/badminton.h"
#include "../include/logger.h"
#include "../include/config.h"
#include <stdarg.h>

static FILE* log_file = NULL;
static const char* log_level_names[] = {
    "DEBUG", "INFO", "WARN", "ERROR", "FATAL"
};

void init_logger(void) {
    log_file = fopen(LOG_FILE, "a");
    if (log_file == NULL) {
        printf("警告：无法创建日志文件\n");
        return;
    }
    
    // 写入启动日志
    time_t now = time(NULL);
    char* time_str = ctime(&now);
    time_str[strlen(time_str) - 1] = '\0'; // 移除换行符
    
    fprintf(log_file, "\n=== 系统启动 %s ===\n", time_str);
    fflush(log_file);
}

void log_message(LogLevel level, const char* format, ...) {
    if (log_file == NULL) {
        return;
    }
    
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    // 写入时间戳和日志级别
    fprintf(log_file, "[%04d-%02d-%02d %02d:%02d:%02d] [%s] ",
            tm_info->tm_year + 1900, tm_info->tm_mon + 1, tm_info->tm_mday,
            tm_info->tm_hour, tm_info->tm_min, tm_info->tm_sec,
            log_level_names[level]);
    
    // 写入日志内容
    va_list args;
    va_start(args, format);
    vfprintf(log_file, format, args);
    va_end(args);
    
    fprintf(log_file, "\n");
    fflush(log_file);
}void log_user_action(int user_id, const char* action) {
    if (current_user != NULL) {
        log_message(LOG_INFO, "用户操作 - ID:%d 用户名:%s 操作:%s", 
                   user_id, current_user->username, action);
    } else {
        log_message(LOG_INFO, "用户操作 - ID:%d 操作:%s", user_id, action);
    }
}

void log_system_event(const char* event) {
    log_message(LOG_INFO, "系统事件 - %s", event);
}

void log_error(const char* error_msg) {
    log_message(LOG_ERROR, "错误 - %s", error_msg);
}

void cleanup_logger(void) {
    if (log_file != NULL) {
        time_t now = time(NULL);
        char* time_str = ctime(&now);
        time_str[strlen(time_str) - 1] = '\0'; // 移除换行符
        
        fprintf(log_file, "=== 系统关闭 %s ===\n\n", time_str);
        fclose(log_file);
        log_file = NULL;
    }
}