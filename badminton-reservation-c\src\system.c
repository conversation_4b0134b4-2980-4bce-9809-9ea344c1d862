#include "../include/badminton.h"

void init_system(void) {
    // 初始化日志系统
    init_logger();
    LOG_INFO_MSG("开始初始化系统");
    
    // 初始化系统配置
    init_system_config();
    load_system_config();
    
    // 初始化系统数据
    memset(&system_data, 0, sizeof(SystemData));
    
    // 设置初始ID
    system_data.next_user_id = 1;
    system_data.next_venue_id = 1;
    system_data.next_reservation_id = 1;
    system_data.next_order_id = 1;
    
    // 创建默认管理员账户
    strcpy(system_data.users[0].username, "admin");
    strcpy(system_data.users[0].password, "admin123");
    strcpy(system_data.users[0].email, "<EMAIL>");
    strcpy(system_data.users[0].phone, "13800138000");
    strcpy(system_data.users[0].real_name, "系统管理员");
    system_data.users[0].id = system_data.next_user_id++;
    system_data.users[0].role = ROLE_SUPER_ADMIN;
    system_data.users[0].status = STATUS_ACTIVE;
    system_data.users[0].created_at = time(NULL);
    system_data.users[0].updated_at = time(NULL);
    system_data.user_count = 1;
    
    // 创建默认场地
    strcpy(system_data.venues[0].name, "1号场地");
    strcpy(system_data.venues[0].description, "标准羽毛球场地，设备齐全");
    system_data.venues[0].id = system_data.next_venue_id++;
    system_data.venues[0].capacity = 4;
    system_data.venues[0].price_per_hour = 80.0;
    strcpy(system_data.venues[0].equipment, "羽毛球拍、羽毛球、计分牌");
    system_data.venues[0].status = VENUE_AVAILABLE;
    system_data.venues[0].created_at = time(NULL);
    system_data.venues[0].updated_at = time(NULL);
    
    strcpy(system_data.venues[1].name, "2号场地");
    strcpy(system_data.venues[1].description, "高级羽毛球场地，专业比赛标准");
    system_data.venues[1].id = system_data.next_venue_id++;
    system_data.venues[1].capacity = 4;
    system_data.venues[1].price_per_hour = 120.0;
    strcpy(system_data.venues[1].equipment, "专业羽毛球拍、比赛用球、电子计分牌");
    system_data.venues[1].status = VENUE_AVAILABLE;
    system_data.venues[1].created_at = time(NULL);
    system_data.venues[1].updated_at = time(NULL);
    
    system_data.venue_count = 2;
    
    LOG_INFO_MSG("系统初始化完成");
    printf("系统初始化完成！\n");
    printf("默认管理员账户: admin / admin123\n\n");
}

void cleanup_system(void) {
    LOG_INFO_MSG("开始清理系统资源");
    
    // 保存所有数据
    save_data();
    
    // 保存系统配置
    save_system_config();
    
    // 清理日志系统
    cleanup_logger();
    
    printf("系统资源清理完成。\n");
}