#include "../include/badminton.h"

void show_main_menu(void) {
    int choice;
    char username[MAX_NAME_LEN], password[MAX_PASSWORD_LEN];
    
    while (1) {
        clear_screen();
        printf("===========================================\n");
        printf("     羽毛球馆预约管理系统 - 主菜单\n");
        printf("===========================================\n");
        printf("1. 用户登录\n");
        printf("2. 用户注册\n");
        printf("3. 查看场地信息\n");
        printf("0. 退出系统\n");
        printf("===========================================\n");
        printf("请选择操作: ");
        
        scanf("%d", &choice);
        
        switch (choice) {
            case 1:
                printf("请输入用户名: ");
                scanf("%s", username);
                printf("请输入密码: ");
                scanf("%s", password);
                
                if (login_user(username, password)) {
                    if (current_user->role == ROLE_ADMIN || current_user->role == ROLE_SUPER_ADMIN) {
                        show_admin_menu();
                    } else {
                        show_user_menu();
                    }
                }
                pause_screen();
                break;
                
            case 2:
                {
                    char email[MAX_EMAIL_LEN], phone[MAX_PHONE_LEN], real_name[MAX_NAME_LEN];
                    printf("请输入用户名: ");
                    scanf("%s", username);
                    printf("请输入密码: ");
                    scanf("%s", password);
                    printf("请输入邮箱: ");
                    scanf("%s", email);
                    printf("请输入手机号: ");
                    scanf("%s", phone);
                    printf("请输入真实姓名: ");
                    scanf("%s", real_name);
                    
                    register_user(username, password, email, phone, real_name);
                    pause_screen();
                }
                break;
                
            case 3:
                list_available_venues();
                pause_screen();
                break;
                
            case 0:
                printf("感谢使用羽毛球馆预约管理系统！\n");
                return;
                
            default:
                printf("无效选择，请重新输入！\n");
                pause_screen();
                break;
        }
    }
}void show_user_menu(void) {
    int choice;
    
    while (1) {
        clear_screen();
        printf("===========================================\n");
        printf("     用户菜单 - 欢迎 %s\n", current_user->real_name);
        printf("===========================================\n");
        printf("1. 查看场地信息\n");
        printf("2. 预约场地\n");
        printf("3. 我的预约\n");
        printf("4. 我的订单\n");
        printf("5. 支付订单\n");
        printf("6. 取消预约\n");
        printf("7. 修改个人信息\n");
        printf("8. 修改密码\n");
        printf("0. 退出登录\n");
        printf("===========================================\n");
        printf("请选择操作: ");
        
        scanf("%d", &choice);
        
        switch (choice) {
            case 1:
                list_available_venues();
                pause_screen();
                break;
                
            case 2:
                handle_user_operations();
                break;
                
            case 3:
                list_user_reservations(current_user->id);
                pause_screen();
                break;
                
            case 4:
                list_user_orders(current_user->id);
                pause_screen();
                break;
                
            case 5:
                {
                    int order_id;
                    printf("请输入订单ID: ");
                    scanf("%d", &order_id);
                    process_payment(order_id);
                    pause_screen();
                }
                break;
                
            case 6:
                {
                    int reservation_id;
                    printf("请输入预约ID: ");
                    scanf("%d", &reservation_id);
                    cancel_reservation(reservation_id);
                    pause_screen();
                }
                break;
                
            case 7:
                {
                    char email[MAX_EMAIL_LEN], phone[MAX_PHONE_LEN], real_name[MAX_NAME_LEN];
                    printf("请输入新邮箱: ");
                    scanf("%s", email);
                    printf("请输入新手机号: ");
                    scanf("%s", phone);
                    printf("请输入新真实姓名: ");
                    scanf("%s", real_name);
                    
                    update_user_profile(current_user->id, email, phone, real_name);
                    pause_screen();
                }
                break;
                
            case 8:
                {
                    char old_password[MAX_PASSWORD_LEN], new_password[MAX_PASSWORD_LEN];
                    printf("请输入原密码: ");
                    scanf("%s", old_password);
                    printf("请输入新密码: ");
                    scanf("%s", new_password);
                    
                    change_password(current_user->id, old_password, new_password);
                    pause_screen();
                }
                break;
                
            case 0:
                logout_user();
                return;
                
            default:
                printf("无效选择，请重新输入！\n");
                pause_screen();
                break;
        }
    }
}void show_admin_menu(void) {
    int choice;
    
    while (1) {
        clear_screen();
        printf("===========================================\n");
        printf("     管理员菜单 - 欢迎 %s\n", current_user->real_name);
        printf("===========================================\n");
        printf("1. 场地管理\n");
        printf("2. 预约管理\n");
        printf("3. 订单管理\n");
        printf("4. 用户管理\n");
        printf("5. 数据统计\n");
        printf("0. 退出登录\n");
        printf("===========================================\n");
        printf("请选择操作: ");
        
        scanf("%d", &choice);
        
        switch (choice) {
            case 1:
                handle_admin_operations();
                break;
                
            case 2:
                list_all_reservations();
                pause_screen();
                break;
                
            case 3:
                list_all_orders();
                pause_screen();
                break;
                
            case 4:
                printf("用户管理功能开发中...\n");
                pause_screen();
                break;
                
            case 5:
                printf("数据统计功能开发中...\n");
                pause_screen();
                break;
                
            case 0:
                logout_user();
                return;
                
            default:
                printf("无效选择，请重新输入！\n");
                pause_screen();
                break;
        }
    }
}

void handle_user_operations(void) {
    int venue_id;
    char start_str[20], end_str[20], notes[MAX_DESC_LEN];
    struct tm start_tm, end_tm;
    time_t start_time, end_time;
    
    list_available_venues();
    
    printf("请输入要预约的场地ID: ");
    scanf("%d", &venue_id);
    
    printf("请输入开始时间 (格式: YYYY-MM-DD HH:MM): ");
    scanf("%s %s", start_str, start_str + 11);
    
    printf("请输入结束时间 (格式: YYYY-MM-DD HH:MM): ");
    scanf("%s %s", end_str, end_str + 11);
    
    printf("请输入备注 (可选): ");
    getchar(); // 清除缓冲区
    fgets(notes, sizeof(notes), stdin);
    notes[strcspn(notes, "\n")] = 0; // 移除换行符
    
    // 解析时间字符串
    memset(&start_tm, 0, sizeof(struct tm));
    memset(&end_tm, 0, sizeof(struct tm));
    
    sscanf(start_str, "%d-%d-%d %d:%d", 
           &start_tm.tm_year, &start_tm.tm_mon, &start_tm.tm_mday,
           &start_tm.tm_hour, &start_tm.tm_min);
    start_tm.tm_year -= 1900;
    start_tm.tm_mon -= 1;
    
    sscanf(end_str, "%d-%d-%d %d:%d", 
           &end_tm.tm_year, &end_tm.tm_mon, &end_tm.tm_mday,
           &end_tm.tm_hour, &end_tm.tm_min);
    end_tm.tm_year -= 1900;
    end_tm.tm_mon -= 1;
    
    start_time = mktime(&start_tm);
    end_time = mktime(&end_tm);
    
    int reservation_id = create_reservation(current_user->id, venue_id, start_time, end_time, notes);
    if (reservation_id > 0) {
        printf("是否立即创建订单？(1-是, 0-否): ");
        int create_order_choice;
        scanf("%d", &create_order_choice);
        
        if (create_order_choice == 1) {
            int order_id = create_order(reservation_id);
            if (order_id > 0) {
                printf("是否立即支付？(1-是, 0-否): ");
                int pay_choice;
                scanf("%d", &pay_choice);
                
                if (pay_choice == 1) {
                    process_payment(order_id);
                }
            }
        }
    }
    
    pause_screen();
}

void handle_admin_operations(void) {
    int choice;
    
    while (1) {
        clear_screen();
        printf("===========================================\n");
        printf("           场地管理菜单\n");
        printf("===========================================\n");
        printf("1. 查看所有场地\n");
        printf("2. 添加场地\n");
        printf("3. 修改场地\n");
        printf("4. 删除场地\n");
        printf("0. 返回上级菜单\n");
        printf("===========================================\n");
        printf("请选择操作: ");
        
        scanf("%d", &choice);
        
        switch (choice) {
            case 1:
                list_venues();
                pause_screen();
                break;
                
            case 2:
                {
                    char name[MAX_NAME_LEN], description[MAX_DESC_LEN], equipment[MAX_DESC_LEN];
                    int capacity;
                    double price;
                    
                    printf("请输入场地名称: ");
                    scanf("%s", name);
                    printf("请输入场地描述: ");
                    getchar();
                    fgets(description, sizeof(description), stdin);
                    description[strcspn(description, "\n")] = 0;
                    printf("请输入容纳人数: ");
                    scanf("%d", &capacity);
                    printf("请输入每小时价格: ");
                    scanf("%lf", &price);
                    printf("请输入设备配置: ");
                    getchar();
                    fgets(equipment, sizeof(equipment), stdin);
                    equipment[strcspn(equipment, "\n")] = 0;
                    
                    add_venue(name, description, capacity, price, equipment);
                    pause_screen();
                }
                break;
                
            case 3:
                printf("修改场地功能开发中...\n");
                pause_screen();
                break;
                
            case 4:
                {
                    int venue_id;
                    list_venues();
                    printf("请输入要删除的场地ID: ");
                    scanf("%d", &venue_id);
                    delete_venue(venue_id);
                    pause_screen();
                }
                break;
                
            case 0:
                return;
                
            default:
                printf("无效选择，请重新输入！\n");
                pause_screen();
                break;
        }
    }
}