#include "../include/badminton.h"

int add_venue(const char* name, const char* description, int capacity, double price_per_hour, const char* equipment) {
    if (system_data.venue_count >= MAX_VENUES) {
        printf("错误：场地数量已达上限！\n");
        return 0;
    }
    
    Venue* new_venue = &system_data.venues[system_data.venue_count];
    new_venue->id = system_data.next_venue_id++;
    strcpy(new_venue->name, name);
    strcpy(new_venue->description, description);
    new_venue->capacity = capacity;
    new_venue->price_per_hour = price_per_hour;
    strcpy(new_venue->equipment, equipment);
    new_venue->status = VENUE_AVAILABLE;
    new_venue->created_at = time(NULL);
    new_venue->updated_at = time(NULL);
    
    system_data.venue_count++;
    
    printf("场地添加成功！场地ID: %d\n", new_venue->id);
    return new_venue->id;
}

int update_venue(int venue_id, const char* name, const char* description, int capacity, double price_per_hour, const char* equipment) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            strcpy(system_data.venues[i].name, name);
            strcpy(system_data.venues[i].description, description);
            system_data.venues[i].capacity = capacity;
            system_data.venues[i].price_per_hour = price_per_hour;
            strcpy(system_data.venues[i].equipment, equipment);
            system_data.venues[i].updated_at = time(NULL);
            
            printf("场地信息更新成功！\n");
            return 1;
        }
    }
    printf("错误：场地不存在！\n");
    return 0;
}int delete_venue(int venue_id) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            // 检查是否有未完成的预约
            for (int j = 0; j < system_data.reservation_count; j++) {
                if (system_data.reservations[j].venue_id == venue_id && 
                    (system_data.reservations[j].status == RESERVATION_PENDING || 
                     system_data.reservations[j].status == RESERVATION_CONFIRMED)) {
                    printf("错误：该场地还有未完成的预约，无法删除！\n");
                    return 0;
                }
            }
            
            // 移动数组元素
            for (int k = i; k < system_data.venue_count - 1; k++) {
                system_data.venues[k] = system_data.venues[k + 1];
            }
            system_data.venue_count--;
            
            printf("场地删除成功！\n");
            return 1;
        }
    }
    printf("错误：场地不存在！\n");
    return 0;
}

Venue* get_venue_by_id(int venue_id) {
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].id == venue_id) {
            return &system_data.venues[i];
        }
    }
    return NULL;
}

void list_venues(void) {
    printf("\n==================== 场地列表 ====================\n");
    printf("ID\t名称\t\t容量\t价格/小时\t状态\n");
    printf("================================================\n");
    
    for (int i = 0; i < system_data.venue_count; i++) {
        const char* status_str;
        switch (system_data.venues[i].status) {
            case VENUE_AVAILABLE: status_str = "可用"; break;
            case VENUE_MAINTENANCE: status_str = "维护中"; break;
            case VENUE_DISABLED: status_str = "已禁用"; break;
            default: status_str = "未知"; break;
        }
        
        printf("%d\t%-12s\t%d\t%.2f\t\t%s\n", 
               system_data.venues[i].id,
               system_data.venues[i].name,
               system_data.venues[i].capacity,
               system_data.venues[i].price_per_hour,
               status_str);
    }
    printf("================================================\n");
}

void list_available_venues(void) {
    printf("\n================= 可用场地列表 =================\n");
    printf("ID\t名称\t\t容量\t价格/小时\t描述\n");
    printf("================================================\n");
    
    for (int i = 0; i < system_data.venue_count; i++) {
        if (system_data.venues[i].status == VENUE_AVAILABLE) {
            printf("%d\t%-12s\t%d\t%.2f\t\t%s\n", 
                   system_data.venues[i].id,
                   system_data.venues[i].name,
                   system_data.venues[i].capacity,
                   system_data.venues[i].price_per_hour,
                   system_data.venues[i].description);
        }
    }
    printf("================================================\n");
}