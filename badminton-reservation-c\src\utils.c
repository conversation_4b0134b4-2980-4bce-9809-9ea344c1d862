#include "../include/badminton.h"

void clear_screen(void) {
#ifdef _WIN32
    system("cls");
#else
    system("clear");
#endif
}

void pause_screen(void) {
    printf("\n按任意键继续...");
    getchar();
    getchar();
}

char* format_time(time_t time) {
    static char buffer[20];
    struct tm* tm_info = localtime(&time);
    strftime(buffer, sizeof(buffer), "%Y-%m-%d %H:%M", tm_info);
    return buffer;
}

int validate_email(const char* email) {
    if (email == NULL || strlen(email) == 0) {
        return 0;
    }
    
    // 简单的邮箱格式验证
    const char* at_pos = strchr(email, '@');
    if (at_pos == NULL) {
        return 0;
    }
    
    const char* dot_pos = strrchr(at_pos, '.');
    if (dot_pos == NULL || dot_pos <= at_pos + 1) {
        return 0;
    }
    
    return 1;
}

int validate_phone(const char* phone) {
    if (phone == NULL || strlen(phone) != 11) {
        return 0;
    }
    
    // 检查是否都是数字
    for (int i = 0; i < 11; i++) {
        if (!isdigit(phone[i])) {
            return 0;
        }
    }
    
    // 检查是否以1开头
    if (phone[0] != '1') {
        return 0;
    }
    
    return 1;
}

void generate_order_no(char* order_no) {
    time_t now = time(NULL);
    struct tm* tm_info = localtime(&now);
    
    // 格式：年月日时分秒 + 随机数
    sprintf(order_no, "%04d%02d%02d%02d%02d%02d%04d",
            tm_info->tm_year + 1900,
            tm_info->tm_mon + 1,
            tm_info->tm_mday,
            tm_info->tm_hour,
            tm_info->tm_min,
            tm_info->tm_sec,
            rand() % 10000);
}