# 羽毛球馆预约系统 - 支付系统集成文档

## 概述

本文档描述了羽毛球馆预约管理系统的支付系统集成实现，包括订单创建、多种支付方式、支付状态管理、退款处理、订单超时机制等核心功能。

## 系统架构

### 1. 支付系统架构设计

```
支付系统
    |
    ├── 订单管理
    │   ├── 订单创建
    │   ├── 订单查询
    │   ├── 订单取消
    │   └── 订单超时处理
    |
    ├── 支付处理
    │   ├── 支付宝支付
    │   ├── 微信支付
    │   ├── 现金支付
    │   └── 银行卡支付
    |
    ├── 支付状态管理
    │   ├── 待支付状态
    │   ├── 已支付状态
    │   ├── 支付失败状态
    │   ├── 已取消状态
    │   ├── 已超时状态
    │   └── 已退款状态
    |
    └── 退款管理
        ├── 退款申请
        ├── 退款处理
        ├── 退款状态跟踪
        └── 退款记录查询
```

### 2. 数据模型设计

**订单数据结构：**
```c
typedef struct {
    int id;                           // 订单唯一标识
    int reservation_id;               // 关联预约ID
    char order_no[32];                // 订单号
    double amount;                    // 订单金额
    PaymentStatus payment_status;     // 支付状态
    PaymentMethod payment_method;     // 支付方式
    char transaction_id[64];          // 第三方支付交易号
    char payment_desc[200];           // 支付描述
    time_t paid_at;                   // 支付时间
    time_t created_at;                // 创建时间
    time_t timeout_at;                // 订单超时时间
    double refund_amount;             // 退款金额
    time_t refunded_at;               // 退款时间
} Order;
```

**支付状态枚举：**
```c
typedef enum {
    PAYMENT_PENDING = 1,    // 待支付状态
    PAYMENT_PAID = 2,       // 已支付状态
    PAYMENT_REFUNDED = 3,   // 已退款状态
    PAYMENT_FAILED = 4,     // 支付失败状态
    PAYMENT_CANCELLED = 5,  // 已取消状态
    PAYMENT_TIMEOUT = 6     // 已超时状态
} PaymentStatus;
```

**支付方式枚举：**
```c
typedef enum {
    PAYMENT_METHOD_ALIPAY = 1,  // 支付宝
    PAYMENT_METHOD_WECHAT = 2,  // 微信支付
    PAYMENT_METHOD_CASH = 3,    // 现金支付
    PAYMENT_METHOD_CARD = 4     // 银行卡
} PaymentMethod;
```

## 核心功能实现

### 1. 订单创建功能

**函数签名：**
```c
int create_order(int reservation_id);
```

**功能特性：**
- ✅ 预约状态验证（必须为待支付状态）
- ✅ 订单数量限制检查
- ✅ 自动生成唯一订单号
- ✅ 自动设置订单超时时间（30分钟）
- ✅ 初始化所有订单字段
- ✅ 关联预约信息

**实现逻辑：**
```c
int create_order(int reservation_id) {
    // 1. 查找并验证预约
    Reservation* reservation = get_reservation_by_id(reservation_id);
    if (!reservation || reservation->status != RESERVATION_PENDING) {
        return 0;
    }
    
    // 2. 检查订单数量限制
    if (system_data.order_count >= MAX_ORDERS) {
        return 0;
    }
    
    // 3. 创建订单
    Order* new_order = &system_data.orders[system_data.order_count];
    new_order->id = system_data.next_order_id++;
    new_order->reservation_id = reservation_id;
    generate_order_no(new_order->order_no);
    new_order->amount = reservation->total_amount;
    new_order->payment_status = PAYMENT_PENDING;
    new_order->timeout_at = time(NULL) + 30 * 60;  // 30分钟超时
    
    system_data.order_count++;
    return new_order->id;
}
```

### 2. 多支付方式处理

**函数签名：**
```c
int process_payment_with_method(int order_id, PaymentMethod method, const char* transaction_id);
```

**支持的支付方式：**
- **支付宝支付**：模拟支付宝SDK调用
- **微信支付**：模拟微信支付SDK调用
- **现金支付**：线下现金收款
- **银行卡支付**：POS机刷卡支付

**功能特性：**
- ✅ 订单状态验证
- ✅ 订单超时检查
- ✅ 支付方式选择
- ✅ 交易号生成/验证
- ✅ 支付结果处理
- ✅ 预约状态同步更新

**实现逻辑：**
```c
int process_payment_with_method(int order_id, PaymentMethod method, const char* transaction_id) {
    Order* order = get_order_by_id(order_id);
    
    // 1. 订单验证
    if (!order || order->payment_status != PAYMENT_PENDING) {
        return 0;
    }
    
    // 2. 超时检查
    if (time(NULL) > order->timeout_at) {
        order->payment_status = PAYMENT_TIMEOUT;
        return 0;
    }
    
    // 3. 模拟支付处理
    printf("正在使用%s支付...\n", get_payment_method_string(method));
    
    // 4. 更新订单状态
    order->payment_status = PAYMENT_PAID;
    order->payment_method = method;
    order->paid_at = time(NULL);
    
    // 5. 生成交易号
    if (transaction_id && strlen(transaction_id) > 0) {
        strcpy(order->transaction_id, transaction_id);
    } else {
        snprintf(order->transaction_id, sizeof(order->transaction_id), 
                "TXN_%ld_%d", time(NULL), order->id);
    }
    
    // 6. 更新预约状态
    Reservation* reservation = get_reservation_by_id(order->reservation_id);
    if (reservation) {
        reservation->status = RESERVATION_CONFIRMED;
        reservation->updated_at = time(NULL);
    }
    
    return 1;
}
```

### 3. 支付状态管理

**状态流转图：**
```
PENDING (待支付) → PAID (已支付) → REFUNDED (已退款)
       ↓              ↓
   CANCELLED      COMPLETED
   (已取消)       (交易完成)
       ↓
   TIMEOUT
   (已超时)
       ↓
   FAILED
   (支付失败)
```

**状态转换规则：**
- 新建订单默认为待支付状态
- 支付成功后转为已支付状态
- 用户主动取消转为已取消状态
- 超过超时时间转为已超时状态
- 支付失败转为支付失败状态
- 已支付订单可以申请退款

**状态显示映射：**
```c
const char* get_payment_status_string(PaymentStatus status) {
    switch (status) {
        case PAYMENT_PENDING: return "待支付";
        case PAYMENT_PAID: return "已支付";
        case PAYMENT_REFUNDED: return "已退款";
        case PAYMENT_FAILED: return "支付失败";
        case PAYMENT_CANCELLED: return "已取消";
        case PAYMENT_TIMEOUT: return "已超时";
        default: return "未知状态";
    }
}
```

### 4. 订单取消功能

**函数签名：**
```c
int cancel_order(int order_id);
```

**功能特性：**
- ✅ 订单状态验证
- ✅ 已支付订单保护
- ✅ 重复取消检查
- ✅ 预约状态保持

**取消规则：**
- 只有待支付状态的订单可以取消
- 已支付的订单需要通过退款流程
- 已取消的订单不能重复取消
- 取消后预约保持待支付状态，允许重新创建订单

### 5. 退款处理功能

**函数签名：**
```c
int refund_order(int order_id, double refund_amount);
```

**功能特性：**
- ✅ 订单支付状态验证
- ✅ 退款金额验证
- ✅ 重复退款检查
- ✅ 退款处理模拟
- ✅ 预约状态同步更新

**退款规则：**
- 只有已支付的订单可以退款
- 退款金额不能超过订单金额
- 退款金额必须大于0
- 退款后预约状态变为已取消
- 记录退款时间和金额

**实现逻辑：**
```c
int refund_order(int order_id, double refund_amount) {
    Order* order = get_order_by_id(order_id);
    
    // 1. 订单验证
    if (!order || order->payment_status != PAYMENT_PAID) {
        return 0;
    }
    
    // 2. 金额验证
    if (refund_amount <= 0 || refund_amount > order->amount) {
        return 0;
    }
    
    // 3. 重复退款检查
    if (order->payment_status == PAYMENT_REFUNDED) {
        return 0;
    }
    
    // 4. 处理退款
    printf("正在处理退款...\n");
    order->payment_status = PAYMENT_REFUNDED;
    order->refund_amount = refund_amount;
    order->refunded_at = time(NULL);
    
    // 5. 更新预约状态
    Reservation* reservation = get_reservation_by_id(order->reservation_id);
    if (reservation) {
        reservation->status = RESERVATION_CANCELLED;
        reservation->updated_at = time(NULL);
    }
    
    return 1;
}
```

### 6. 订单超时处理

**函数签名：**
```c
int check_order_timeout(void);
```

**功能特性：**
- ✅ 批量超时检查
- ✅ 自动状态更新
- ✅ 超时统计
- ✅ 预约状态保护

**超时机制：**
- 订单创建时设置30分钟超时时间
- 系统定期检查超时订单
- 超时订单自动标记为超时状态
- 预约保持待支付状态，允许重新支付

**实现逻辑：**
```c
int check_order_timeout(void) {
    time_t now = time(NULL);
    int timeout_count = 0;
    
    for (int i = 0; i < system_data.order_count; i++) {
        if (system_data.orders[i].payment_status == PAYMENT_PENDING && 
            now > system_data.orders[i].timeout_at) {
            
            system_data.orders[i].payment_status = PAYMENT_TIMEOUT;
            timeout_count++;
        }
    }
    
    return timeout_count;
}
```

## 订单查询和展示

### 1. 订单详情展示

**函数签名：**
```c
void show_order_details(int order_id);
```

**显示内容：**
- 订单基本信息（ID、订单号、金额）
- 用户信息（姓名、联系方式）
- 预约信息（场地、时间）
- 支付信息（状态、方式、交易号）
- 时间信息（创建、支付、退款时间）
- 超时信息（剩余时间、超时状态）

**显示格式：**
```
==================== 订单详情 ====================
订单ID：1
订单号：ORD_20250626_001
用户姓名：张三
预约ID：1
场地名称：1号场地
预约时间：2025-06-27 09:00 - 2025-06-27 11:00
订单金额：120.00 元
支付状态：已支付
支付方式：支付宝
交易号：TXN_1719456789_1
支付时间：2025-06-26 15:30:00
创建时间：2025-06-26 15:00:00
================================================
```

### 2. 用户订单列表

**函数签名：**
```c
void list_user_orders(int user_id);
```

**功能特性：**
- ✅ 用户权限过滤
- ✅ 订单状态显示
- ✅ 时间格式化
- ✅ 表格化展示

### 3. 管理员订单查询

**函数签名：**
```c
void list_all_orders(void);
```

**功能特性：**
- ✅ 全局订单查询
- ✅ 用户信息关联
- ✅ 预约信息关联
- ✅ 多维度信息展示

## 支付安全机制

### 1. 订单验证

**验证项目：**
- 订单存在性验证
- 订单状态验证
- 订单超时验证
- 金额一致性验证
- 用户权限验证

### 2. 支付验证

**验证机制：**
- 交易号唯一性验证
- 支付金额验证
- 支付方式验证
- 重复支付防护
- 支付时间验证

### 3. 数据完整性

**保护措施：**
- 订单状态原子性更新
- 预约状态同步更新
- 时间戳准确记录
- 错误状态回滚
- 数据一致性检查

## 业务逻辑设计

### 1. 订单生命周期

**生命周期阶段：**
1. **订单创建**：基于预约创建订单
2. **等待支付**：用户选择支付方式
3. **支付处理**：执行支付操作
4. **支付确认**：验证支付结果
5. **订单完成**：更新相关状态
6. **可能退款**：处理退款请求

### 2. 支付流程设计

**支付流程：**
```
用户发起支付 → 选择支付方式 → 调用支付接口 → 等待支付结果 
     ↓
支付成功 → 更新订单状态 → 更新预约状态 → 发送确认通知
     ↓
支付失败 → 更新订单状态 → 允许重新支付 → 记录失败原因
```

### 3. 退款流程设计

**退款流程：**
```
用户申请退款 → 验证退款条件 → 计算退款金额 → 调用退款接口
     ↓
退款成功 → 更新订单状态 → 取消预约 → 发送退款通知
     ↓
退款失败 → 记录失败原因 → 人工处理 → 状态回滚
```

## 性能优化

### 1. 查询优化

**优化策略：**
- 订单ID索引优化
- 用户订单关联优化
- 状态过滤优化
- 时间范围查询优化

### 2. 内存优化

**优化方案：**
- 合理设置MAX_ORDERS常量
- 优化字符串存储
- 减少内存碎片
- 订单数据压缩

### 3. 并发优化

**并发控制：**
- 订单创建原子性
- 支付状态更新锁定
- 重复支付防护
- 数据一致性保证

## 扩展功能设计

### 1. 分期支付

**功能设计：**
```c
typedef struct {
    int order_id;
    int installment_count;        // 分期数
    double installment_amount;    // 每期金额
    int paid_installments;        // 已支付期数
    time_t next_payment_date;     // 下次支付日期
} InstallmentPlan;
```

### 2. 优惠券系统

**功能设计：**
```c
typedef struct {
    int coupon_id;
    char coupon_code[32];
    double discount_amount;       // 优惠金额
    double discount_rate;         // 优惠比例
    time_t valid_from;           // 有效期开始
    time_t valid_to;             // 有效期结束
    int usage_limit;             // 使用次数限制
    int used_count;              // 已使用次数
} Coupon;
```

### 3. 支付回调处理

**功能设计：**
```c
typedef struct {
    char callback_url[256];
    char callback_data[1024];
    time_t callback_time;
    int retry_count;
    int is_verified;
} PaymentCallback;
```

### 4. 支付统计分析

**功能设计：**
```c
typedef struct {
    time_t date;
    double total_amount;          // 总交易金额
    int total_orders;            // 总订单数
    int paid_orders;             // 已支付订单数
    int refund_orders;           // 退款订单数
    double refund_amount;        // 退款金额
    PaymentMethod popular_method; // 热门支付方式
} PaymentStatistics;
```

## 测试用例

### 1. 订单创建测试

**测试场景：**
```c
// 正常订单创建
int reservation_id = create_reservation(1, 1, future_time, future_time + 3600, "测试");
int order_id = create_order(reservation_id);

// 重复创建测试
int duplicate_order = create_order(reservation_id);  // 应该失败

// 无效预约测试
int invalid_order = create_order(999);  // 应该失败

// 预约状态错误测试
confirm_reservation(reservation_id);
int wrong_status_order = create_order(reservation_id);  // 应该失败
```

### 2. 支付处理测试

**测试场景：**
```c
// 支付宝支付测试
int result1 = process_payment_with_method(order_id, PAYMENT_METHOD_ALIPAY, "ALI_TXN_123");

// 微信支付测试
int result2 = process_payment_with_method(order_id, PAYMENT_METHOD_WECHAT, "WX_TXN_456");

// 重复支付测试
int result3 = process_payment_with_method(order_id, PAYMENT_METHOD_ALIPAY, "ALI_TXN_789");  // 应该失败

// 超时订单支付测试
Order* order = get_order_by_id(order_id);
order->timeout_at = time(NULL) - 3600;  // 设置为已超时
int result4 = process_payment_with_method(order_id, PAYMENT_METHOD_ALIPAY, "ALI_TXN_999");  // 应该失败
```

### 3. 退款处理测试

**测试场景：**
```c
// 正常退款测试
int refund_result1 = refund_order(order_id, 100.0);

// 超额退款测试
int refund_result2 = refund_order(order_id, 1000.0);  // 应该失败

// 未支付订单退款测试
int pending_order = create_order(reservation_id);
int refund_result3 = refund_order(pending_order, 50.0);  // 应该失败

// 重复退款测试
int refund_result4 = refund_order(order_id, 50.0);  // 应该失败
```

### 4. 订单超时测试

**测试场景：**
```c
// 创建即将超时的订单
int order_id = create_order(reservation_id);
Order* order = get_order_by_id(order_id);
order->timeout_at = time(NULL) + 60;  // 1分钟后超时

// 等待超时
sleep(70);

// 检查超时处理
int timeout_count = check_order_timeout();

// 验证超时后不能支付
int payment_result = process_payment_with_method(order_id, PAYMENT_METHOD_ALIPAY, "TXN_123");  // 应该失败
```

## 错误处理

### 1. 订单错误

**错误类型：**
- 订单不存在
- 订单状态错误
- 订单已超时
- 订单数量超限

**处理策略：**
```c
int validate_order_operation(int order_id, PaymentStatus required_status) {
    Order* order = get_order_by_id(order_id);
    if (!order) {
        printf("错误：订单不存在！\n");
        return 0;
    }
    
    if (order->payment_status != required_status) {
        printf("错误：订单状态不正确！当前状态：%s\n", 
               get_payment_status_string(order->payment_status));
        return 0;
    }
    
    if (order->payment_status == PAYMENT_PENDING && time(NULL) > order->timeout_at) {
        order->payment_status = PAYMENT_TIMEOUT;
        printf("错误：订单已超时！\n");
        return 0;
    }
    
    return 1;
}
```

### 2. 支付错误

**错误类型：**
- 支付方式无效
- 交易号重复
- 支付金额不匹配
- 网络连接失败

**处理策略：**
- 详细的错误信息记录
- 支付重试机制
- 异常状态恢复
- 用户友好提示

### 3. 系统错误

**错误类型：**
- 内存不足
- 数据损坏
- 并发冲突

**处理策略：**
- 异常捕获和恢复
- 数据备份和恢复
- 错误日志记录

## 部署配置

### 1. 系统配置

**配置参数：**
```c
#define MAX_ORDERS 500                 // 最大订单数量
#define ORDER_TIMEOUT_MINUTES 30       // 订单超时时间（分钟）
#define MAX_REFUND_DAYS 7              // 最大退款天数
#define MIN_REFUND_AMOUNT 0.01         // 最小退款金额
#define MAX_TRANSACTION_ID_LEN 64      // 交易号最大长度
```

### 2. 支付配置

**支付参数：**
```c
typedef struct {
    char alipay_app_id[32];
    char alipay_private_key[512];
    char wechat_app_id[32];
    char wechat_mch_id[32];
    char wechat_api_key[64];
    int payment_timeout_seconds;
    int max_retry_count;
} PaymentConfig;
```

### 3. 测试数据

**测试订单：**
```c
void init_test_orders() {
    // 创建测试预约
    time_t tomorrow = time(NULL) + 24 * 3600;
    int reservation_id = create_reservation(1, 1, tomorrow + 9 * 3600, tomorrow + 11 * 3600, "测试预约");
    
    // 创建测试订单
    int order_id = create_order(reservation_id);
    
    // 模拟支付
    process_payment_with_method(order_id, PAYMENT_METHOD_ALIPAY, "TEST_TXN_001");
}
```

## 版本历史

| 版本 | 日期 | 修改内容 | 修改人 |
|------|------|----------|--------|
| 1.0 | 2025-06-26 | 初始版本，完成基础支付系统功能 | Alex |

## 总结

本支付系统实现了完整的订单生命周期管理和多种支付方式支持，通过严格的状态管理和安全验证机制，确保了支付数据的准确性和安全性。

系统支持支付宝、微信支付、现金支付和银行卡支付四种支付方式，提供了完整的订单创建、支付处理、退款管理和超时处理功能。通过优化的查询算法和缓存策略，保证了系统的性能和响应速度。

扩展功能设计为系统的未来发展提供了良好的基础，包括分期支付、优惠券系统、支付回调处理和统计分析等高级功能。完整的测试用例和错误处理机制确保了系统的可靠性和用户体验。

支付系统作为羽毛球馆预约系统的核心组件，通过合理的架构设计和严格的业务规则实现，为用户提供了安全、便捷、可靠的支付服务体验。