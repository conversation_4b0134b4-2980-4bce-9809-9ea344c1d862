@echo off
echo 编译羽毛球馆预约管理系统...
echo.

REM 创建build目录
if not exist build mkdir build

REM 编译所有源文件
echo 编译源文件...
gcc -Wall -Wextra -std=c99 -Iinclude -c src\main.c -o build\main.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\system.c -o build\system.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\data.c -o build\data.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\user.c -o build\user.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\venue.c -o build\venue.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\reservation.c -o build\reservation.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\order.c -o build\order.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\utils.c -o build\utils.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\menu.c -o build\menu.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\config.c -o build\config.o
gcc -Wall -Wextra -std=c99 -Iinclude -c src\logger.c -o build\logger.o

REM 链接生成可执行文件
echo 链接生成可执行文件...
gcc build\main.o build\system.o build\data.o build\user.o build\venue.o build\reservation.o build\order.o build\utils.o build\menu.o build\config.o build\logger.o -o badminton_system.exe

if exist badminton_system.exe (
    echo.
    echo 编译成功！可执行文件：badminton_system.exe
    echo 运行程序请执行：run.bat
) else (
    echo.
    echo 编译失败！
)

echo.
pause