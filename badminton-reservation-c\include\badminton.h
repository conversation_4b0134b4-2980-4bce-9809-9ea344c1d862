#ifndef BADMINTON_H
#define BADMINTON_H

#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <ctype.h>

// 常量定义
#define MAX_NAME_LEN 50
#define MAX_DESC_LEN 200
#define MAX_PHONE_LEN 20
#define MAX_EMAIL_LEN 100
#define MAX_PASSWORD_LEN 50
#define MAX_VENUES 50
#define MAX_USERS 1000
#define MAX_RESERVATIONS 2000
#define MAX_ORDERS 2000

// 用户角色枚举
typedef enum {
    ROLE_USER = 1,
    ROLE_COACH = 2,
    ROLE_ADMIN = 3,
    ROLE_SUPER_ADMIN = 4
} UserRole;

// 用户状态枚举
typedef enum {
    STATUS_ACTIVE = 1,
    STATUS_INACTIVE = 2,
    STATUS_BANNED = 3
} UserStatus;

// 场地状态枚举
typedef enum {
    VENUE_AVAILABLE = 1,
    VENUE_MAINTENANCE = 2,
    VENUE_DISABLED = 3
} VenueStatus;

// 预约状态枚举
typedef enum {
    RESERVATION_PENDING = 1,
    RESERVATION_CONFIRMED = 2,
    RESERVATION_COMPLETED = 3,
    RESERVATION_CANCELLED = 4
} ReservationStatus;

// 支付状态枚举
typedef enum {
    PAYMENT_PENDING = 1,
    PAYMENT_PAID = 2,
    PAYMENT_REFUNDED = 3,
    PAYMENT_FAILED = 4
} PaymentStatus;

// 用户结构体
typedef struct {
    int id;
    char username[MAX_NAME_LEN];
    char password[MAX_PASSWORD_LEN];
    char email[MAX_EMAIL_LEN];
    char phone[MAX_PHONE_LEN];
    char real_name[MAX_NAME_LEN];
    UserRole role;
    UserStatus status;
    time_t created_at;
    time_t updated_at;
} User;

// 场地结构体
typedef struct {
    int id;
    char name[MAX_NAME_LEN];
    char description[MAX_DESC_LEN];
    int capacity;
    double price_per_hour;
    char equipment[MAX_DESC_LEN];
    VenueStatus status;
    time_t created_at;
    time_t updated_at;
} Venue;

// 预约结构体
typedef struct {
    int id;
    int user_id;
    int venue_id;
    time_t start_time;
    time_t end_time;
    double total_amount;
    ReservationStatus status;
    char notes[MAX_DESC_LEN];
    time_t created_at;
    time_t updated_at;
} Reservation;

// 订单结构体
typedef struct {
    int id;
    int reservation_id;
    char order_no[32];
    double amount;
    PaymentStatus payment_status;
    time_t paid_at;
    time_t created_at;
} Order;

// 系统数据结构
typedef struct {
    User users[MAX_USERS];
    Venue venues[MAX_VENUES];
    Reservation reservations[MAX_RESERVATIONS];
    Order orders[MAX_ORDERS];
    int user_count;
    int venue_count;
    int reservation_count;
    int order_count;
    int next_user_id;
    int next_venue_id;
    int next_reservation_id;
    int next_order_id;
} SystemData;

// 全局变量声明
extern SystemData system_data;
extern User* current_user;

// 函数声明
// 系统初始化和数据管理
void init_system(void);
void load_data(void);
void save_data(void);
void cleanup_system(void);

// 用户管理
int register_user(const char* username, const char* password, const char* email, const char* phone, const char* real_name);
User* login_user(const char* username, const char* password);
void logout_user(void);
int update_user_profile(int user_id, const char* email, const char* phone, const char* real_name);
int change_password(int user_id, const char* old_password, const char* new_password);

// 场地管理
int add_venue(const char* name, const char* description, int capacity, double price_per_hour, const char* equipment);
int update_venue(int venue_id, const char* name, const char* description, int capacity, double price_per_hour, const char* equipment);
int delete_venue(int venue_id);
Venue* get_venue_by_id(int venue_id);
void list_venues(void);
void list_available_venues(void);

// 预约管理
int create_reservation(int user_id, int venue_id, time_t start_time, time_t end_time, const char* notes);
int cancel_reservation(int reservation_id);
int check_venue_availability(int venue_id, time_t start_time, time_t end_time);
void list_user_reservations(int user_id);
void list_all_reservations(void);

// 订单管理
int create_order(int reservation_id);
int process_payment(int order_id);
void list_user_orders(int user_id);
void list_all_orders(void);

// 工具函数
void clear_screen(void);
void pause_screen(void);
char* format_time(time_t time);
int validate_email(const char* email);
int validate_phone(const char* phone);
void generate_order_no(char* order_no);

// 菜单系统
void show_main_menu(void);
void show_user_menu(void);
void show_admin_menu(void);
void handle_user_operations(void);
void handle_admin_operations(void);

#endif // BADMINTON_H